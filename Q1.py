import pandas as pd
import numpy as np
import warnings
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

warnings.filterwarnings('ignore')

@dataclass
class AnalysisConfig:
    """分析配置参数"""
    FRAME_RATE: float = 30.0
    SMOOTHING_WINDOW: int = 5
    KEY_BODY_POINTS: List[int] = None
    FOOT_POINTS: List[int] = None
    DISPLACEMENT_WINDOW: int = 30
    MIN_FLIGHT_TIME: float = 0.25
    MOVEMENT_THRESHOLD: int = 30

    def __post_init__(self):
        if self.KEY_BODY_POINTS is None:
            self.KEY_BODY_POINTS = [11, 12, 23, 24]  # 肩膀和髋部
        if self.FOOT_POINTS is None:
            self.FOOT_POINTS = [29, 30, 31, 32]

class JumpAnalyzer:
    def __init__(self, excel_path: str, athlete_name: str, actual_score: float, config: AnalysisConfig = None):
        self.athlete_name = athlete_name
        self.actual_score = actual_score
        self.config = config or AnalysisConfig()
        self.frame_rate = self.config.FRAME_RATE

        print(f"读取{athlete_name}的数据...")
        self.data = pd.read_excel(excel_path)
        print(f"数据读取成功: {len(self.data)}帧")

        self.results: Dict[str, Any] = {}

        # 初始化分析数据属性
        self.smooth_data: Optional[pd.DataFrame] = None
        self.com_x: Optional[np.ndarray] = None
        self.com_y: Optional[np.ndarray] = None
        self.vx: Optional[np.ndarray] = None
        self.vy: Optional[np.ndarray] = None
        self.foot_min_y: Optional[np.ndarray] = None
        self.foot_center_x: Optional[np.ndarray] = None
        self.takeoff_frame: Optional[int] = None
        self.landing_frame: Optional[int] = None
        self.flight_time: Optional[float] = None

    def preprocess_data(self) -> None:
        """数据预处理：平滑数据并计算运动学参数"""
        print("数据预处理...")

        self.smooth_data = self.data.copy()
        coordinate_cols = self._get_coordinate_columns()

        # 批量平滑处理
        self._smooth_coordinates(coordinate_cols)

        self._calculate_center_of_mass()
        self._calculate_kinematics()

    def _get_coordinate_columns(self) -> List[str]:
        """获取坐标列名"""
        return [col for col in self.data.columns if '_X' in col or '_Y' in col]

    def _smooth_coordinates(self, coordinate_cols: List[str]) -> None:
        """平滑坐标数据"""
        window = self.config.SMOOTHING_WINDOW

        for col in coordinate_cols:
            if col in self.data.columns and len(self.data[col]) > window:
                self.smooth_data[col] = self.data[col].rolling(
                    window=window, center=True, min_periods=1
                ).mean()

    def _calculate_center_of_mass(self) -> None:
        """计算重心位置"""
        com_x_list = []
        com_y_list = []
        key_points = self.config.KEY_BODY_POINTS

        for _, row in self.smooth_data.iterrows():
            x_coords, y_coords = self._extract_key_point_coordinates(row, key_points)

            if x_coords and y_coords:
                com_x_list.append(np.mean(x_coords))
                com_y_list.append(np.mean(y_coords))
            else:
                # 如果主要节点缺失，使用所有可用节点
                fallback_x, fallback_y = self._extract_all_coordinates(row)
                com_x_list.append(np.mean(fallback_x) if fallback_x else 0)
                com_y_list.append(np.mean(fallback_y) if fallback_y else 0)

        self.com_x = np.array(com_x_list)
        self.com_y = np.array(com_y_list)

    def _extract_key_point_coordinates(self, row: pd.Series, key_points: List[int]) -> Tuple[List[float], List[float]]:
        """提取关键点坐标"""
        x_coords = [row[f'{p}_X'] for p in key_points if f'{p}_X' in row.index and not pd.isna(row[f'{p}_X'])]
        y_coords = [row[f'{p}_Y'] for p in key_points if f'{p}_Y' in row.index and not pd.isna(row[f'{p}_Y'])]
        return x_coords, y_coords

    def _extract_all_coordinates(self, row: pd.Series) -> Tuple[List[float], List[float]]:
        """提取所有可用坐标"""
        all_x = [row[col] for col in row.index if '_X' in col and not pd.isna(row[col])]
        all_y = [row[col] for col in row.index if '_Y' in col and not pd.isna(row[col])]
        return all_x, all_y

    def _calculate_kinematics(self) -> None:
        """计算运动学参数：速度和足部追踪"""
        dt = 1.0 / self.frame_rate

        # 计算重心速度
        self.vx = np.gradient(self.com_x, dt)
        self.vy = np.gradient(self.com_y, dt)

        # 计算足部追踪数据
        self._calculate_foot_tracking()

    def _calculate_foot_tracking(self) -> None:
        """计算足部追踪数据"""
        foot_min_y_list = []
        foot_center_x_list = []
        foot_points = self.config.FOOT_POINTS

        for _, row in self.smooth_data.iterrows():
            foot_y_coords, foot_x_coords = self._extract_foot_coordinates(row, foot_points)

            # 计算足部最低点和中心点
            foot_min_y_list.append(min(foot_y_coords) if foot_y_coords else 0)
            foot_center_x_list.append(np.mean(foot_x_coords) if foot_x_coords else 0)

        self.foot_min_y = np.array(foot_min_y_list)
        self.foot_center_x = np.array(foot_center_x_list)

    def _extract_foot_coordinates(self, row: pd.Series, foot_points: List[int]) -> Tuple[List[float], List[float]]:
        """提取足部坐标"""
        foot_y_coords = [row[f'{p}_Y'] for p in foot_points
                        if f'{p}_Y' in row.index and not pd.isna(row[f'{p}_Y'])]
        foot_x_coords = [row[f'{p}_X'] for p in foot_points
                        if f'{p}_X' in row.index and not pd.isna(row[f'{p}_X'])]
        return foot_y_coords, foot_x_coords

    def detect_takeoff_moment(self) -> int:
        """检测起跳时刻 - 基于大幅横向移动"""
        print("检测起跳时刻 - 基于大幅横向移动...")

        horizontal_displacements = self._calculate_horizontal_displacements()
        displacement_threshold = self._get_displacement_threshold(horizontal_displacements)

        print(f"横向位移阈值: {displacement_threshold:.1f}像素")

        candidates = self._find_takeoff_candidates(horizontal_displacements, displacement_threshold)

        self.takeoff_frame = self._select_best_takeoff_candidate(candidates, horizontal_displacements)

        print(f"起跳时刻: 第{self.takeoff_frame}帧 ({self.takeoff_frame/self.frame_rate:.3f}秒)")
        print(f"对应横向位移: {horizontal_displacements[self.takeoff_frame] if self.takeoff_frame < len(horizontal_displacements) else 0:.1f}像素")

        self.results['takeoff_frame'] = self.takeoff_frame
        return self.takeoff_frame

    def _calculate_horizontal_displacements(self) -> np.ndarray:
        """计算横向位移"""
        horizontal_displacements = []
        window_size = self.config.DISPLACEMENT_WINDOW

        for i in range(len(self.foot_center_x) - window_size):
            start_x = self.foot_center_x[i]
            end_x = self.foot_center_x[i + window_size]
            displacement = abs(end_x - start_x)
            horizontal_displacements.append(displacement)

        return np.array(horizontal_displacements)

    def _get_displacement_threshold(self, horizontal_displacements: np.ndarray) -> float:
        """获取位移阈值"""
        return np.percentile(horizontal_displacements, 80)

    def _find_takeoff_candidates(self, horizontal_displacements: np.ndarray, threshold: float) -> List[int]:
        """寻找起跳候选点"""
        candidates = []

        for i in range(10, len(horizontal_displacements)):
            current_displacement = horizontal_displacements[i]

            if current_displacement > threshold:
                if self._is_valid_takeoff_candidate(i, horizontal_displacements, threshold):
                    candidates.append(i)
                    print(f"发现起跳候选: 第{i}帧, 横向位移: {current_displacement:.1f}像素")

        return candidates

    def _is_valid_takeoff_candidate(self, frame_idx: int, horizontal_displacements: np.ndarray, threshold: float) -> bool:
        """验证起跳候选点是否有效"""
        # 验证这是第一次大幅移动
        is_first_major_move = self._is_first_major_movement(frame_idx, horizontal_displacements, threshold)

        # 验证足部确实离地
        foot_lifted = self._is_foot_lifted(frame_idx)

        # 验证重心有上升
        has_height_gain = self._has_height_gain(frame_idx)

        return is_first_major_move and foot_lifted and has_height_gain

    def _is_first_major_movement(self, frame_idx: int, horizontal_displacements: np.ndarray, threshold: float) -> bool:
        """检查是否为第一次大幅移动"""
        for j in range(max(0, frame_idx-20), frame_idx):
            if horizontal_displacements[j] > threshold * 0.8:
                return False
        return True

    def _is_foot_lifted(self, frame_idx: int) -> bool:
        """检查足部是否离地"""
        ground_baseline = np.mean(self.foot_min_y[:max(10, frame_idx-10)])
        return self.foot_min_y[frame_idx] < ground_baseline - 8

    def _has_height_gain(self, frame_idx: int) -> bool:
        """检查重心是否有上升"""
        if frame_idx + 15 < len(self.com_y):
            height_gain = self.com_y[frame_idx] - np.min(self.com_y[frame_idx:frame_idx+15])
            return height_gain > 10
        return False

    def _select_best_takeoff_candidate(self, candidates: List[int], horizontal_displacements: np.ndarray) -> int:
        """选择最佳起跳候选点"""
        if candidates:
            return candidates[0]  # 选择第一个符合条件的
        else:
            # 备用方法：寻找最大横向位移
            max_disp_idx = np.argmax(horizontal_displacements)
            print("未找到明确起跳点，选择最大位移点")
            return max_disp_idx

    def detect_landing_moment(self) -> int:
        """检测落地时刻"""
        print("检测落地时刻...")

        ground_baseline = self._calculate_ground_baseline()
        print(f"地面基准线: {ground_baseline:.2f}像素")

        search_start = self._get_landing_search_start()

        # 主要检测方法
        landing_frame = self._detect_primary_landing(search_start, ground_baseline)

        if landing_frame is None:
            # 备用检测方法
            landing_frame = self._detect_backup_landing(search_start, ground_baseline)

        if landing_frame is None:
            # 最后备用：估算落地时刻
            landing_frame = self._estimate_landing_frame()

        self.landing_frame = landing_frame
        self.flight_time = (self.landing_frame - self.takeoff_frame) / self.frame_rate

        print(f"落地时刻: 第{self.landing_frame}帧 ({self.landing_frame/self.frame_rate:.3f}秒)")
        print(f"滞空时间: {self.flight_time:.3f}秒")

        self.results['landing_frame'] = self.landing_frame
        self.results['flight_time'] = self.flight_time

        return self.landing_frame

    def _calculate_ground_baseline(self) -> float:
        """计算地面基准线"""
        return np.mean(self.foot_min_y[:self.takeoff_frame])

    def _get_landing_search_start(self) -> int:
        """获取落地搜索起始点"""
        min_flight_frames = int(self.config.MIN_FLIGHT_TIME * self.frame_rate)
        return self.takeoff_frame + min_flight_frames

    def _detect_primary_landing(self, search_start: int, ground_baseline: float) -> Optional[int]:
        """主要落地检测方法"""
        for i in range(search_start, len(self.foot_min_y) - 5):
            if self._is_valid_landing_frame(i, ground_baseline):
                return i
        return None

    def _is_valid_landing_frame(self, frame_idx: int, ground_baseline: float) -> bool:
        """检查是否为有效的落地帧"""
        # 条件1: 足部重新接近地面
        near_ground = abs(self.foot_min_y[frame_idx] - ground_baseline) < 20

        # 条件2: 横向移动基本停止
        movement_stopped = self._is_movement_stopped(frame_idx)

        # 条件3: 重心不再明显上升
        not_rising = self._is_not_rising(frame_idx)

        # 条件4: 后续保持相对稳定
        stable_afterwards = self._is_stable_afterwards(frame_idx)

        return near_ground and movement_stopped and not_rising and stable_afterwards

    def _is_movement_stopped(self, frame_idx: int) -> bool:
        """检查横向移动是否停止"""
        if frame_idx >= 10:
            recent_displacement = abs(self.foot_center_x[frame_idx] - self.foot_center_x[frame_idx-10])
            return recent_displacement < 15
        return True

    def _is_not_rising(self, frame_idx: int) -> bool:
        """检查重心是否不再上升"""
        if frame_idx < len(self.vy):
            return self.vy[frame_idx] <= 2.0
        return True

    def _is_stable_afterwards(self, frame_idx: int) -> bool:
        """检查后续是否稳定"""
        if frame_idx + 10 < len(self.foot_min_y):
            future_foot_std = np.std(self.foot_min_y[frame_idx:frame_idx+10])
            return future_foot_std < 8
        return True

    def _detect_backup_landing(self, search_start: int, ground_baseline: float) -> Optional[int]:
        """备用落地检测方法"""
        print("使用备用落地检测...")

        for i in range(search_start, len(self.foot_center_x) - 10):
            if i >= 15:
                early_movement = abs(self.foot_center_x[i-5] - self.foot_center_x[i-15])
                recent_movement = abs(self.foot_center_x[i+5] - self.foot_center_x[i-5])

                if recent_movement < early_movement * 0.3:  # 移动显著减缓
                    if abs(self.foot_min_y[i] - ground_baseline) < 30:
                        return i
        return None

    def _estimate_landing_frame(self) -> int:
        """估算落地帧"""
        estimated_flight_time = 0.5  # 估算0.5秒飞行
        return self.takeoff_frame + int(estimated_flight_time * self.frame_rate)

    def analyze_flight_phase(self) -> Dict[str, float]:
        """分析滞空运动"""
        print("分析滞空运动...")

        flight_data = self._extract_flight_data()
        takeoff_params = self._calculate_takeoff_parameters(flight_data)
        trajectory_params = self._analyze_trajectory(flight_data)

        flight_analysis = {
            **takeoff_params,
            **trajectory_params,
            '像素米转换比': self._calculate_pixel_to_meter_ratio(trajectory_params['水平飞行距离'])
        }

        self._print_flight_analysis(flight_analysis)
        self.results['flight_analysis'] = flight_analysis
        return flight_analysis

    def _extract_flight_data(self) -> Dict[str, np.ndarray]:
        """提取飞行期数据"""
        flight_indices = range(self.takeoff_frame, self.landing_frame + 1)
        return {
            'com_x': self.com_x[flight_indices],
            'com_y': self.com_y[flight_indices]
        }

    def _calculate_takeoff_parameters(self, flight_data: Dict[str, np.ndarray]) -> Dict[str, float]:
        """计算起跳参数"""
        if self.takeoff_frame < len(self.vx) and self.takeoff_frame < len(self.vy):
            takeoff_vx = self.vx[self.takeoff_frame]
            takeoff_vy = self.vy[self.takeoff_frame]
            takeoff_speed = np.sqrt(takeoff_vx**2 + takeoff_vy**2)
            takeoff_angle = self._calculate_takeoff_angle(flight_data)
        else:
            takeoff_speed = 0
            takeoff_angle = 25.0

        return {
            '起跳初速度': takeoff_speed,
            '起跳角度': takeoff_angle
        }

    def _calculate_takeoff_angle(self, flight_data: Dict[str, np.ndarray]) -> float:
        """计算起跳角度"""
        flight_com_x = flight_data['com_x']
        flight_com_y = flight_data['com_y']

        # 改进的角度计算：基于整个飞行轨迹
        total_horizontal = abs(flight_com_x[-1] - flight_com_x[0])
        total_vertical = flight_com_x[0] - np.min(flight_com_y)  # 最大上升高度

        if total_horizontal > 0:
            takeoff_angle = np.arctan(total_vertical/total_horizontal) * 180 / np.pi
            return max(5, min(45, takeoff_angle))  # 限制在合理范围
        else:
            return 25.0

    def _analyze_trajectory(self, flight_data: Dict[str, np.ndarray]) -> Dict[str, float]:
        """分析飞行轨迹"""
        flight_com_x = flight_data['com_x']
        flight_com_y = flight_data['com_y']

        max_height = flight_com_y[0] - np.min(flight_com_y) if len(flight_com_y) > 0 else 0
        horizontal_distance = abs(flight_com_x[-1] - flight_com_x[0]) if len(flight_com_x) > 1 else 0

        return {
            '最大飞行高度': max_height,
            '水平飞行距离': horizontal_distance
        }

    def _calculate_pixel_to_meter_ratio(self, horizontal_distance: float) -> float:
        """计算像素到米的转换比例"""
        if horizontal_distance > 0:
            return self.actual_score / horizontal_distance
        else:
            return 0.01

    def _print_flight_analysis(self, flight_analysis: Dict[str, float]) -> None:
        """打印飞行分析结果"""
        print(f"起跳速度: {flight_analysis['起跳初速度']:.1f} 像素/秒")
        print(f"起跳角度: {flight_analysis['起跳角度']:.1f}度")
        print(f"最大高度: {flight_analysis['最大飞行高度']:.1f} 像素")
        print(f"水平距离: {flight_analysis['水平飞行距离']:.1f} 像素")
        print(f"转换比例: 1像素 = {flight_analysis['像素米转换比']:.4f}米")

    def analyze_horizontal_movement(self) -> Dict[str, Any]:
        """分析横向移动模式，找到第一次大幅度左到右移动"""
        print("分析横向移动模式...")

        cumulative_displacement = self._calculate_cumulative_displacement()
        movement_threshold = self.config.MOVEMENT_THRESHOLD
        significant_moves = self._find_significant_moves(cumulative_displacement, movement_threshold)

        self.horizontal_analysis = {
            '累积位移': cumulative_displacement,
            '重要移动点': significant_moves,
            '移动阈值': movement_threshold
        }

        print(f"检测到{len(significant_moves)}个重要横向移动点")
        if significant_moves:
            print(f"第一次大幅移动: 第{significant_moves[0]}帧")

        return self.horizontal_analysis

    def _calculate_cumulative_displacement(self) -> np.ndarray:
        """计算累积横向位移"""
        initial_x = self.foot_center_x[0]
        cumulative_displacement = []

        for i in range(len(self.foot_center_x)):
            displacement = self.foot_center_x[i] - initial_x
            cumulative_displacement.append(displacement)

        return np.array(cumulative_displacement)

    def _find_significant_moves(self, cumulative_displacement: np.ndarray, threshold: int) -> List[int]:
        """寻找重要的横向移动点"""
        significant_moves = []

        for i in range(1, len(cumulative_displacement)):
            if self._is_significant_move(i, cumulative_displacement, threshold):
                significant_moves.append(i)

        return significant_moves

    def _is_significant_move(self, frame_idx: int, cumulative_displacement: np.ndarray, threshold: int) -> bool:
        """检查是否为重要移动"""
        # 检查位移是否超过阈值
        if cumulative_displacement[frame_idx] <= threshold:
            return False

        # 检查这是否是第一次达到这个位移
        is_first_time = all(cumulative_displacement[:frame_idx] <= threshold)

        if not is_first_time:
            return False

        # 验证这个移动是连续的（不是跳跃式的数据错误）
        return self._is_continuous_movement(frame_idx, cumulative_displacement)

    def _is_continuous_movement(self, frame_idx: int, cumulative_displacement: np.ndarray) -> bool:
        """检查移动是否连续"""
        if frame_idx >= 5:
            recent_trend = cumulative_displacement[frame_idx] - cumulative_displacement[frame_idx-5]
            return recent_trend > 10  # 近5帧有连续移动
        return True

    def improved_takeoff_detection(self) -> int:
        """改进的起跳检测：结合横向移动和高度变化"""
        print("执行改进的起跳检测...")

        h_analysis = self.analyze_horizontal_movement()

        if h_analysis['重要移动点']:
            self.takeoff_frame = self._detect_precise_takeoff(h_analysis['重要移动点'][0])
        else:
            print("未检测到明显横向移动，使用备用检测...")
            self.takeoff_frame = self._traditional_takeoff_detection()

        self.results['takeoff_frame'] = self.takeoff_frame
        return self.takeoff_frame

    def _detect_precise_takeoff(self, first_major_move: int) -> int:
        """精确检测起跳时刻"""
        search_start = max(0, first_major_move - 10)
        search_end = min(len(self.com_y) - 15, first_major_move + 5)

        print(f"在第{search_start}-{search_end}帧范围内精确定位起跳时刻")

        best_takeoff, max_combined_score = self._find_best_takeoff_candidate(search_start, search_end)

        if best_takeoff:
            print(f"起跳时刻确定: 第{best_takeoff}帧, 综合得分: {max_combined_score:.1f}")
            return best_takeoff
        else:
            print(f"使用横向移动点作为起跳时刻: 第{first_major_move}帧")
            return first_major_move

    def _find_best_takeoff_candidate(self, search_start: int, search_end: int) -> Tuple[Optional[int], float]:
        """寻找最佳起跳候选点"""
        best_takeoff = None
        max_combined_score = 0

        for i in range(search_start, search_end):
            combined_score = self._calculate_takeoff_score(i)

            if combined_score > max_combined_score and combined_score > 40:  # 最低分数要求
                max_combined_score = combined_score
                best_takeoff = i

            if combined_score > 60:  # 发现高分候选
                scores = self._get_individual_scores(i)
                print(f"候选帧{i}: 综合得分{combined_score:.1f} "
                      f"(高度{scores['height']:.0f}+足部{scores['foot']:.0f}+"
                      f"速度{scores['velocity']:.0f}+位移{scores['displacement']:.0f})")

        return best_takeoff, max_combined_score

    def _calculate_takeoff_score(self, frame_idx: int) -> float:
        """计算起跳评分"""
        scores = self._get_individual_scores(frame_idx)
        return (scores['height'] * 0.3 + scores['foot'] * 0.2 +
                scores['velocity'] * 0.2 + scores['displacement'] * 0.3)

    def _get_individual_scores(self, frame_idx: int) -> Dict[str, float]:
        """获取各项评分"""
        return {
            'height': self._calculate_height_score(frame_idx),
            'foot': self._calculate_foot_score(frame_idx),
            'velocity': self._calculate_velocity_score(frame_idx),
            'displacement': self._calculate_displacement_score(frame_idx)
        }

    def _calculate_height_score(self, frame_idx: int) -> float:
        """计算高度增益评分"""
        if frame_idx + 15 < len(self.com_y):
            height_gain = self.com_y[frame_idx] - np.min(self.com_y[frame_idx:frame_idx+15])
            return min(100, height_gain * 5)
        return 0

    def _calculate_foot_score(self, frame_idx: int) -> float:
        """计算足部离地评分"""
        ground_baseline = np.mean(self.foot_min_y[:frame_idx]) if frame_idx > 5 else np.mean(self.foot_min_y[:10])
        foot_lift = ground_baseline - self.foot_min_y[frame_idx]
        return min(100, foot_lift * 10) if foot_lift > 0 else 0

    def _calculate_velocity_score(self, frame_idx: int) -> float:
        """计算向前速度评分"""
        forward_velocity = self.vx[frame_idx] if frame_idx < len(self.vx) else 0
        return min(100, forward_velocity * 2) if forward_velocity > 0 else 0

    def _calculate_displacement_score(self, frame_idx: int) -> float:
        """计算后续横向位移评分"""
        if frame_idx + 20 < len(self.foot_center_x):
            future_displacement = abs(self.foot_center_x[frame_idx+20] - self.foot_center_x[frame_idx])
            return min(100, future_displacement)
        return 0

    def _traditional_takeoff_detection(self) -> int:
        """传统起跳检测作为备用"""
        # 寻找重心最低点附近的起跳
        min_height_idx = np.argmin(self.com_y[:int(len(self.com_y)*0.8)])

        # 从最低点开始向后寻找起跳
        for i in range(min_height_idx, min(len(self.com_y)-10, min_height_idx + 20)):
            if i < len(self.vy) and self.vy[i] > 1.0:
                return i

        return min_height_idx

    def comprehensive_analysis(self) -> Dict[str, Any]:
        """综合分析"""
        print(f"开始分析{self.athlete_name} (实际成绩: {self.actual_score}m)")
        print("="*50)

        self.preprocess_data()
        self.improved_takeoff_detection()
        self.detect_landing_moment()
        self.analyze_flight_phase()

        self.generate_report()

        print(f"{self.athlete_name}分析完成")
        return self.results

    def generate_report(self) -> None:
        """生成分析报告"""
        print(f"\n{self.athlete_name} 分析报告")
        print("="*40)

        self._print_basic_info()
        self._print_key_moments()
        self._print_flight_parameters()

    def _print_basic_info(self) -> None:
        """打印基本信息"""
        print(f"基本信息:")
        print(f"  实际跳远成绩: {self.actual_score}m")
        print(f"  视频总时长: {len(self.data)/self.frame_rate:.2f}秒")

    def _print_key_moments(self) -> None:
        """打印关键时刻"""
        print(f"关键时刻:")
        print(f"  起跳时刻: 第{self.results['takeoff_frame']}帧 ({self.results['takeoff_frame']/self.frame_rate:.3f}秒)")
        print(f"  落地时刻: 第{self.results['landing_frame']}帧 ({self.results['landing_frame']/self.frame_rate:.3f}秒)")
        print(f"  滞空时间: {self.results['flight_time']:.3f}秒")

    def _print_flight_parameters(self) -> None:
        """打印滞空运动参数"""
        if 'flight_analysis' in self.results:
            flight_data = self.results['flight_analysis']
            print(f"滞空运动参数:")
            print(f"  起跳速度: {flight_data['起跳初速度']:.1f}像素/秒")
            print(f"  起跳角度: {flight_data['起跳角度']:.1f}度")
            print(f"  最大高度: {flight_data['最大飞行高度']:.1f}像素")
            print(f"  水平距离: {flight_data['水平飞行距离']:.1f}像素")
            print(f"  像素米比例: 1像素 = {flight_data['像素米转换比']:.4f}米")


def analyze_athlete(data_path: str, athlete_name: str, actual_score: float) -> Tuple[Optional[JumpAnalyzer], Optional[Dict[str, Any]]]:
    """分析单个运动员"""
    try:
        analyzer = JumpAnalyzer(data_path, athlete_name, actual_score)
        results = analyzer.comprehensive_analysis()
        return analyzer, results
    except Exception as e:
        print(f"分析{athlete_name}失败: {e}")
        return None, None

def solve_problem1() -> Tuple[Dict[str, JumpAnalyzer], Dict[str, Dict[str, Any]]]:
    """问题1主解决函数"""
    print("立定跳远问题1：起跳落地时刻检测与滞空运动分析")
    print("="*60)

    athletes_data = _get_athletes_data()
    analyzers, all_results = _analyze_all_athletes(athletes_data)

    # 对比分析
    if len(analyzers) == 2:
        print(f"\n对比分析")
        print("-" * 30)
        compare_athletes(analyzers)

    return analyzers, all_results


def _get_athletes_data() -> Dict[str, Dict[str, Any]]:
    """获取运动员数据配置"""
    return {
        '运动者1': {
            'path': r'C:\Users\<USER>\Desktop\E题\附件\附件1\运动者1的跳远位置信息.xlsx',
            'score': 1.58
        },
        '运动者2': {
            'path': r'C:\Users\<USER>\Desktop\E题\附件\附件1\运动者2的跳远位置信息.xlsx',
            'score': 1.15
        }
    }


def _analyze_all_athletes(athletes_data: Dict[str, Dict[str, Any]]) -> Tuple[Dict[str, JumpAnalyzer], Dict[str, Dict[str, Any]]]:
    """分析所有运动员"""
    analyzers = {}
    all_results = {}

    for athlete_name, info in athletes_data.items():
        print(f"\n{athlete_name} 分析")
        print("-" * 30)

        analyzer, results = analyze_athlete(info['path'], athlete_name, info['score'])

        if analyzer and results:
            analyzers[athlete_name] = analyzer
            all_results[athlete_name] = results

    return analyzers, all_results

def compare_athletes(analyzers: Dict[str, JumpAnalyzer]) -> None:
    """对比分析两位运动者"""
    if len(analyzers) != 2:
        return

    names = list(analyzers.keys())
    analyzer1, analyzer2 = analyzers[names[0]], analyzers[names[1]]

    print(f"对比分析 {names[0]} vs {names[1]}:")

    _compare_key_moments(analyzer1, analyzer2)
    _compare_performance(analyzer1, analyzer2)
    _compare_technical_parameters(analyzer1, analyzer2)


def _compare_key_moments(analyzer1: JumpAnalyzer, analyzer2: JumpAnalyzer) -> None:
    """对比关键时刻"""
    print(f"起跳时刻: {analyzer1.results['takeoff_frame']}帧 vs {analyzer2.results['takeoff_frame']}帧")
    print(f"落地时刻: {analyzer1.results['landing_frame']}帧 vs {analyzer2.results['landing_frame']}帧")
    print(f"滞空时间: {analyzer1.results['flight_time']:.3f}s vs {analyzer2.results['flight_time']:.3f}s")


def _compare_performance(analyzer1: JumpAnalyzer, analyzer2: JumpAnalyzer) -> None:
    """对比成绩"""
    score_diff = analyzer1.actual_score - analyzer2.actual_score
    print(f"成绩差异: {score_diff:.2f}m")


def _compare_technical_parameters(analyzer1: JumpAnalyzer, analyzer2: JumpAnalyzer) -> None:
    """对比技术参数"""
    if 'flight_analysis' not in analyzer1.results or 'flight_analysis' not in analyzer2.results:
        return

    flight1 = analyzer1.results['flight_analysis']
    flight2 = analyzer2.results['flight_analysis']

    print(f"水平距离对比: {flight1['水平飞行距离']:.1f} vs {flight2['水平飞行距离']:.1f} 像素")
    print(f"起跳角度对比: {flight1['起跳角度']:.1f} vs {flight2['起跳角度']:.1f} 度")

    _analyze_performance_differences(flight1, flight2)


def _analyze_performance_differences(flight1: Dict[str, float], flight2: Dict[str, float]) -> None:
    """分析成绩差异原因"""
    if flight1['水平飞行距离'] > flight2['水平飞行距离'] * 1.1:
        print("运动者1的水平位移明显更大，这解释了成绩优势")

    if abs(flight1['起跳角度'] - 22) < abs(flight2['起跳角度'] - 22):
        print("运动者1的起跳角度更接近最优角度(22度)")


def export_results(analyzers: Dict[str, JumpAnalyzer], output_file: str = "问题1分析结果.xlsx") -> None:
    """导出分析结果"""
    try:
        summary_data = _prepare_export_data(analyzers)

        if summary_data:
            df = pd.DataFrame(summary_data)
            df.to_excel(output_file, index=False)
            print(f"结果已导出到: {output_file}")
    except Exception as e:
        print(f"导出失败: {e}")


def _prepare_export_data(analyzers: Dict[str, JumpAnalyzer]) -> List[Dict[str, Any]]:
    """准备导出数据"""
    summary_data = []

    for name, analyzer in analyzers.items():
        if hasattr(analyzer, 'results'):
            results = analyzer.results
            summary_data.append({
                '运动员': name,
                '实际成绩(m)': analyzer.actual_score,
                '起跳帧': results.get('takeoff_frame', 0),
                '落地帧': results.get('landing_frame', 0),
                '滞空时间(s)': results.get('flight_time', 0)
            })

    return summary_data

def main() -> None:
    """主函数"""
    print("立定跳远AI姿态分析 - 问题1解决方案")
    print("="*60)

    try:
        analyzers, _ = solve_problem1()

        if analyzers:
            export_results(analyzers)
            _print_final_results(analyzers)

    except Exception as e:
        print(f"程序执行错误: {e}")
        import traceback
        traceback.print_exc()


def _print_final_results(analyzers: Dict[str, JumpAnalyzer]) -> None:
    """打印最终结果"""
    print(f"\n最终答案:")
    print("="*30)

    for name, analyzer in analyzers.items():
        if hasattr(analyzer, 'results'):
            r = analyzer.results
            print(f"{name}:")
            print(f"  起跳时刻: 第{r['takeoff_frame']}帧 ({r['takeoff_frame']/30:.3f}秒)")
            print(f"  落地时刻: 第{r['landing_frame']}帧 ({r['landing_frame']/30:.3f}秒)")
            print(f"  滞空时间: {r['flight_time']:.3f}秒")


if __name__ == "__main__":
    main()