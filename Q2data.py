import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings

warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
class PhysiqueDataProcessor:
    """体质数据预处理与分析系统"""

    def __init__(self, excel_path):
        """初始化并加载数据"""
        self.raw_data = pd.read_excel(excel_path)
        self.processed_data = None
        self.standardized_data = None
        self.feature_categories = None
        self.derived_features = None

    def explore_data(self):
        """探索性数据分析"""
        print("=" * 60)
        print("原始数据概览")
        print("=" * 60)

        # 基本信息
        print(f"\n数据维度: {self.raw_data.shape}")
        print(f"运动员数量: {len(self.raw_data)}")
        print(f"特征数量: {len(self.raw_data.columns)}")

        # 列名和数据类型
        print("\n特征列表:")
        for i, col in enumerate(self.raw_data.columns, 1):
            dtype = self.raw_data[col].dtype
            null_count = self.raw_data[col].isnull().sum()
            print(f"{i:2d}. {col:<20s} | 类型: {dtype} | 缺失值: {null_count}")

        # 缺失值统计
        missing_stats = self.raw_data.isnull().sum()
        if missing_stats.sum() > 0:
            print("\n缺失值分析:")#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
            print(missing_stats[missing_stats > 0])
        else:
            print("\n无缺失值")

        # 数据预览
        print("\n数据样本:")
        print(self.raw_data.head())

        return self.raw_data.info()

    def categorize_features(self):
        """将特征分类"""

        self.feature_categories = {
            '基础信息': ['姓名', '年龄 (岁)', '性别'],

            '体格指标': ['身高 (cm)', '体重 (kg)', '标准体重 (kg)', 'BMI'],

            '身体成分': ['体脂率 (%)', '肌肉重量 (kg)', '肌肉率 (%)',
                         '骨骼肌重量 (kg)', '脂肪重量 (kg)', '去脂体重 (kg)',
                         '骨量 (kg)', '蛋白质重量 (kg)', '蛋白质率 (%)',
                         '水分重量 (kg)', '水分率 (%)'],

            '健康指标': ['内脏脂肪 (等级)', '基础代谢 (kcal)', '营养状态', '体型'],

            '控制建议': ['体重控制量 (kg)', '脂肪控制量 (kg)', '肌肉控制量 (kg)'],

            '综合评价': ['身体得分']
        }

        print("\n特征分类结果:")
        print("=" * 60)
        for category, features in self.feature_categories.items():
            print(f"\n{category} ({len(features)}个):")
            for feat in features:
                if feat in self.raw_data.columns:
                    print(f"  ✓ {feat}")
                else:
                    print(f"  ✗ {feat} (未找到)")

    def preprocess_data(self):
        """数据预处理"""
        print("\n数据预处理...")
        print("=" * 60)

        # 创建处理后的数据副本
        self.processed_data = self.raw_data.copy()

        # 1. 处理性别（转换为数值）
        if '性别' in self.processed_data.columns:
            self.processed_data['性别_编码'] = self.processed_data['性别'].map({'男': 1, '女': 0})
            print("✓ 性别编码完成")

        # 2. 处理营养状态（如果存在）
        if '营养状态' in self.processed_data.columns:
            nutrition_map = {'偏瘦': -1, '正常': 0, '偏胖': 1, '肥胖': 2}
            self.processed_data['营养状态_编码'] = self.processed_data['营养状态'].map(nutrition_map)
            print("✓ 营养状态编码完成")

        # 3. 处理体型（如果存在）
        if '体型' in self.processed_data.columns:
            body_type_map = {'瘦型': -1, '标准': 0, '偏胖': 1, '肥胖': 2}
            self.processed_data['体型_编码'] = self.processed_data['体型'].map(body_type_map)
            print("✓ 体型编码完成")

        # 4. 填充缺失值
        numeric_columns = self.processed_data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if self.processed_data[col].isnull().sum() > 0:
                # 使用中位数填充
                median_value = self.processed_data[col].median()
                self.processed_data[col].fillna(median_value, inplace=True)
                print(f"✓ {col} 缺失值已填充 (中位数: {median_value:.2f})")

        # 5. 异常值检测和处理
        self._handle_outliers()

        print("\n预处理完成！")
        return self.processed_data

    def _handle_outliers(self):
        """处理异常值"""
        print("\n异常值检测...")

        numeric_cols = self.processed_data.select_dtypes(include=[np.number]).columns
        outlier_count = 0

        for col in numeric_cols:
            if col not in ['姓名', '性别_编码']:
                Q1 = self.processed_data[col].quantile(0.25)
                Q3 = self.processed_data[col].quantile(0.75)
                IQR = Q3 - Q1

                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                outliers = self.processed_data[(self.processed_data[col] < lower_bound) |
                                               (self.processed_data[col] > upper_bound)]

                if len(outliers) > 0:
                    outlier_count += len(outliers)
                    print(f"  {col}: {len(outliers)}个异常值")

                    # 使用边界值替换异常值
                    self.processed_data.loc[self.processed_data[col] < lower_bound, col] = lower_bound
                    self.processed_data.loc[self.processed_data[col] > upper_bound, col] = upper_bound

        if outlier_count == 0:
            print("  未发现异常值")
        else:
            print(f"  共处理 {outlier_count} 个异常值")

    def create_derived_features(self):
        """创建衍生特征"""
        print("\n创建衍生特征...")
        print("=" * 60)

        self.derived_features = pd.DataFrame()

        # 1. 身体质量指数类
        if all(col in self.processed_data.columns for col in ['身高 (cm)', '体重 (kg)']):
            # BMI（如果不存在）
            if 'BMI' not in self.processed_data.columns:
                self.derived_features['BMI'] = self.processed_data['体重 (kg)'] / \
                                               (self.processed_data['身高 (cm)'] / 100) ** 2
                print("✓ BMI 计算完成")

            # 体重身高比
            self.derived_features['体重身高比'] = self.processed_data['体重 (kg)'] / \
                                                  self.processed_data['身高 (cm)']
            print("✓ 体重身高比 计算完成")

        # 2. 肌肉力量潜力指标
        if all(col in self.processed_data.columns for col in ['肌肉重量 (kg)', '体重 (kg)']):
            self.derived_features['相对肌肉量'] = self.processed_data['肌肉重量 (kg)'] / \
                                                  self.processed_data['体重 (kg)']
            print("✓ 相对肌肉量 计算完成")

        if all(col in self.processed_data.columns for col in ['骨骼肌重量 (kg)', '身高 (cm)']):
            # 骨骼肌指数 (SMI)
            self.derived_features['骨骼肌指数'] = self.processed_data['骨骼肌重量 (kg)'] / \
                                                  (self.processed_data['身高 (cm)'] / 100) ** 2
            print("✓ 骨骼肌指数 计算完成")

        # 3. 爆发力潜力指标
        if all(col in self.processed_data.columns for col in ['肌肉率 (%)', '体脂率 (%)']):
            # 肌脂比
            self.derived_features['肌脂比'] = self.processed_data['肌肉率 (%)'] / \
                                              (self.processed_data['体脂率 (%)'] + 1)  # 避免除零
            print("✓ 肌脂比 计算完成")

            # 爆发力潜力指数
            self.derived_features['爆发力潜力'] = (self.processed_data['肌肉率 (%)'] *
                                                   (100 - self.processed_data['体脂率 (%)'])) / 100
            print("✓ 爆发力潜力指数 计算完成")

        # 4. 身体成分平衡指标
        if all(col in self.processed_data.columns for col in ['脂肪重量 (kg)', '去脂体重 (kg)']):
            # 脂肪与去脂体重比
            self.derived_features['脂肪去脂比'] = self.processed_data['脂肪重量 (kg)'] / \
                                                  (self.processed_data['去脂体重 (kg)'] + 1)
            print("✓ 脂肪去脂比 计算完成")

        # 5. 代谢效率指标
        if all(col in self.processed_data.columns for col in ['基础代谢 (kcal)', '体重 (kg)']):
            # 单位体重基础代谢率
            self.derived_features['单位代谢率'] = self.processed_data['基础代谢 (kcal)'] / \
                                                  self.processed_data['体重 (kg)']
            print("✓ 单位代谢率 计算完成")

        # 6. 身体优化潜力
        if all(col in self.processed_data.columns for col in ['体重控制量 (kg)', '体重 (kg)']):
            # 体重优化比例
            self.derived_features['体重优化率'] = np.abs(self.processed_data['体重控制量 (kg)']) / \
                                                  self.processed_data['体重 (kg)'] * 100
            print("✓ 体重优化率 计算完成")

        if all(col in self.processed_data.columns for col in ['肌肉控制量 (kg)', '肌肉重量 (kg)']):
            # 肌肉发展潜力
            self.derived_features['肌肉发展潜力'] = self.processed_data['肌肉控制量 (kg)'] / \
                                                    (self.processed_data['肌肉重量 (kg)'] + 1) * 100
            print("✓ 肌肉发展潜力 计算完成")

        # 7. 性别调整指标
        if '性别_编码' in self.processed_data.columns and '身高 (cm)' in self.processed_data.columns:
            # 性别调整身高（男性身高优势标准化）
            mean_height_male = self.processed_data[self.processed_data['性别_编码'] == 1]['身高 (cm)'].mean()
            mean_height_female = self.processed_data[self.processed_data['性别_编码'] == 0]['身高 (cm)'].mean()

            self.derived_features['标准化身高'] = self.processed_data.apply(
                lambda row: row['身高 (cm)'] / mean_height_male if row['性别_编码'] == 1
                else row['身高 (cm)'] / mean_height_female, axis=1
            )
            print("✓ 标准化身高 计算完成")

        # 8. 综合体质评分
        if '身体得分' in self.processed_data.columns:
            # 如果有原始得分，创建加权得分
            self.derived_features['综合体质指数'] = self.processed_data['身体得分']
        else:
            # 创建综合指数
            factors = []
            weights = []

            if '爆发力潜力' in self.derived_features.columns:
                factors.append(self.derived_features['爆发力潜力'])
                weights.append(0.3)

            if '相对肌肉量' in self.derived_features.columns:
                factors.append(self.derived_features['相对肌肉量'] * 100)
                weights.append(0.2)

            if '单位代谢率' in self.derived_features.columns:
                factors.append(self.derived_features['单位代谢率'])
                weights.append(0.1)

            if factors:
                self.derived_features['综合体质指数'] = sum(f * w for f, w in zip(factors, weights))
                print("✓ 综合体质指数 计算完成")

        # 将衍生特征合并到处理后的数据中
        self.processed_data = pd.concat([self.processed_data, self.derived_features], axis=1)

        print(f"\n共创建 {len(self.derived_features.columns)} 个衍生特征")
        return self.derived_features

    def standardize_features(self, method='zscore'):
        """特征标准化

        参数:
            method: 'zscore' (标准化) 或 'minmax' (归一化)
        """
        print(f"\n特征标准化 (方法: {method})...")
        print("=" * 60)

        # 选择需要标准化的数值列
        exclude_cols = ['姓名', '性别', '营养状态', '体型']
        numeric_cols = [col for col in self.processed_data.select_dtypes(include=[np.number]).columns
                        if col not in exclude_cols]

        # 创建标准化数据框
        self.standardized_data = self.processed_data.copy()

        if method == 'zscore':
            scaler = StandardScaler()
            self.standardized_data[numeric_cols] = scaler.fit_transform(
                self.processed_data[numeric_cols]
            )
            print("✓ Z-score标准化完成")

        elif method == 'minmax':
            scaler = MinMaxScaler()
            self.standardized_data[numeric_cols] = scaler.fit_transform(
                self.processed_data[numeric_cols]
            )
            print("✓ Min-Max归一化完成")

        else:
            raise ValueError("方法必须是 'zscore' 或 'minmax'")

        # 保存标准化参数
        self.scaler = scaler
        self.standardized_columns = numeric_cols

        print(f"标准化了 {len(numeric_cols)} 个特征")

        return self.standardized_data

    def get_feature_importance_for_jumping(self):
        """获取对跳远重要的特征排序"""

        print("\n跳远相关特征重要性分析...")
        print("=" * 60)

        # 基于运动生理学知识的特征重要性权重
        feature_importance = {
            # 极高重要性 (权重 > 0.8)
            '爆发力潜力': 0.95,
            '肌肉率 (%)': 0.90,
            '骨骼肌指数': 0.88,
            '相对肌肉量': 0.85,
            '肌脂比': 0.85,

            # 高重要性 (权重 0.6-0.8)
            '骨骼肌重量 (kg)': 0.75,
            '肌肉重量 (kg)': 0.73,
            '身高 (cm)': 0.70,
            '标准化身高': 0.68,
            '去脂体重 (kg)': 0.65,
            '单位代谢率': 0.62,

            # 中等重要性 (权重 0.4-0.6)
            '体脂率 (%)': -0.55,  # 负相关
            '基础代谢 (kcal)': 0.50,
            '蛋白质率 (%)': 0.48,
            '水分率 (%)': 0.45,
            '体重 (kg)': 0.42,

            # 低重要性 (权重 < 0.4)
            '脂肪重量 (kg)': -0.38,  # 负相关
            '内脏脂肪 (等级)': -0.35,  # 负相关
            '年龄 (岁)': -0.20,  # 轻微负相关
            'BMI': -0.15,  # 轻微负相关

            # 参考指标
            '肌肉发展潜力': 0.30,
            '体重优化率': 0.25,
            '综合体质指数': 0.60
        }

        # 筛选实际存在的特征
        available_features = {}
        all_columns = list(self.processed_data.columns) + list(self.derived_features.columns)

        for feature, importance in feature_importance.items():
            if feature in all_columns:
                available_features[feature] = importance

        # 排序
        sorted_features = sorted(available_features.items(),
                                 key=lambda x: abs(x[1]), reverse=True)

        print("\n特征重要性排序（对立定跳远）:")
        for i, (feature, importance) in enumerate(sorted_features[:15], 1):
            direction = "正相关" if importance > 0 else "负相关"
            print(f"{i:2d}. {feature:<20s}: {abs(importance):.2f} ({direction})")

        return sorted_features

    def create_athlete_profiles(self):
        """创建运动员特征画像"""

        print("\n运动员特征画像...")
        print("=" * 60)

        profiles = []

        for idx, row in self.processed_data.iterrows():
            profile = {
                '运动员': row.get('姓名', f'运动者{idx + 3}'),
                '基本信息': {
                    '年龄': row.get('年龄 (岁)', 0),
                    '性别': row.get('性别', 'Unknown'),
                    '身高': row.get('身高 (cm)', 0),
                    '体重': row.get('体重 (kg)', 0),
                },
                '体质优势': [],
                '体质劣势': [],
                '改进建议': []
            }

            # 分析优势
            if row.get('肌肉率 (%)', 0) > self.processed_data['肌肉率 (%)'].mean():
                profile['体质优势'].append('肌肉发达')

            if row.get('体脂率 (%)', 100) < self.processed_data['体脂率 (%)'].mean():
                profile['体质优势'].append('体脂率低')

            if row.get('身高 (cm)', 0) > self.processed_data['身高 (cm)'].mean():
                profile['体质优势'].append('身高优势')

            # 分析劣势
            if row.get('体脂率 (%)', 0) > self.processed_data['体脂率 (%)'].mean() + 5:
                profile['体质劣势'].append('体脂偏高')
                profile['改进建议'].append('减脂训练')

            if row.get('肌肉率 (%)', 100) < self.processed_data['肌肉率 (%)'].mean() - 5:
                profile['体质劣势'].append('肌肉量不足')
                profile['改进建议'].append('增肌训练')

            if row.get('肌肉控制量 (kg)', 0) > 2:
                profile['改进建议'].append(f'建议增加{row.get("肌肉控制量 (kg)", 0):.1f}kg肌肉')

            if row.get('脂肪控制量 (kg)', 0) < -2:
                profile['改进建议'].append(f'建议减少{abs(row.get("脂肪控制量 (kg)", 0)):.1f}kg脂肪')

            profiles.append(profile)
            # 论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
            # 打印个人画像
            print(f"\n{profile['运动员']}:")
            print(f"  基本: {profile['基本信息']['年龄']}岁, {profile['基本信息']['性别']}, "
                  f"{profile['基本信息']['身高']}cm, {profile['基本信息']['体重']}kg")
            if profile['体质优势']:
                print(f"  优势: {', '.join(profile['体质优势'])}")
            if profile['体质劣势']:
                print(f"  劣势: {', '.join(profile['体质劣势'])}")
            if profile['改进建议']:
                print(f"  建议: {', '.join(profile['改进建议'])}")

        return profiles

    def export_processed_data(self, output_path='processed_physique_data.xlsx'):
        """导出处理后的数据"""

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 原始数据
            self.raw_data.to_excel(writer, sheet_name='原始数据', index=False)

            # 处理后的数据
            self.processed_data.to_excel(writer, sheet_name='处理后数据', index=False)

            # 衍生特征
            if self.derived_features is not None:
                self.derived_features.to_excel(writer, sheet_name='衍生特征', index=False)

            # 标准化数据
            if self.standardized_data is not None:
                self.standardized_data.to_excel(writer, sheet_name='标准化数据', index=False)

            # 特征重要性
            importance_df = pd.DataFrame(self.get_feature_importance_for_jumping(),
                                         columns=['特征', '重要性'])
            importance_df.to_excel(writer, sheet_name='特征重要性', index=False)

        print(f"\n数据已导出到: {output_path}")

    def visualize_data_distribution(self):
        """可视化数据分布"""

        fig, axes = plt.subplots(3, 3, figsize=(15, 12))
        fig.suptitle('体质数据分布分析', fontsize=16, fontweight='bold')

        # 选择关键特征进行可视化
        key_features = [
            '身高 (cm)', '体重 (kg)', '体脂率 (%)',
            '肌肉率 (%)', '骨骼肌重量 (kg)', '基础代谢 (kcal)',
            '爆发力潜力', '肌脂比', '综合体质指数'
        ]

        for idx, (ax, feature) in enumerate(zip(axes.flat, key_features)):
            if feature in self.processed_data.columns or feature in self.derived_features.columns:
                data = self.processed_data.get(feature, self.derived_features.get(feature))

                # 绘制直方图和核密度估计
                ax.hist(data, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
                ax2 = ax.twinx()
                data.plot(kind='density', ax=ax2, color='red', linewidth=2)

                ax.set_title(feature, fontsize=10, fontweight='bold')
                ax.set_xlabel('值')
                ax.set_ylabel('频数', color='blue')
                ax2.set_ylabel('密度', color='red')

                # 添加均值线
                mean_val = data.mean()
                ax.axvline(mean_val, color='green', linestyle='--',
                           linewidth=2, label=f'均值: {mean_val:.1f}')
                ax.legend()

        plt.tight_layout()
        plt.savefig('体质数据分布.png', dpi=150, bbox_inches='tight')

        try:
            plt.show()
        except AttributeError:
            # 处理matplotlib版本兼容性问题
            print("\n注意：图形显示存在兼容性问题，但图片已成功保存")

        print("\n分布图已保存为: 体质数据分布.png")


# 使用示例
if __name__ == "__main__":
    # 初始化处理器
    processor = PhysiqueDataProcessor(r"C:\Users\<USER>\Desktop\E题\附件\附件4.xlsx")

    # 1. 数据探索
    processor.explore_data()

    # 2. 特征分类
    processor.categorize_features()

    # 3. 数据预处理
    processor.preprocess_data()

    # 4. 创建衍生特征
    processor.create_derived_features()

    # 5. 特征标准化
    processor.standardize_features(method='zscore')

    # 6. 特征重要性分析
    processor.get_feature_importance_for_jumping()

    # 7. 创建运动员画像
    processor.create_athlete_profiles()

    # 8. 可视化
    processor.visualize_data_distribution()

    # 9. 导出数据
    processor.export_processed_data()

    print("\n数据预处理完成！")
    print("=" * 60)
    print("输出文件:")
    print("1. processed_physique_data.xlsx - 包含所有处理后的数据")
    print("2. 体质数据分布.png - 数据分布可视化")