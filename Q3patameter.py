import numpy as np
from scipy.optimize import differential_evolution, minimize
from sklearn.metrics import mean_squared_error, r2_score
import pandas as pd


class OptimizedBiomechanicalModel:
    def __init__(self):
        self.frame_rate = 30.0
        self.gravity = 9.8
        # 论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
        # 待优化的参数（初始值）
        self.params = {
            'metabolic_base': 1500,
            'muscle_efficiency': 0.40,
            'chain_efficiency': 0.90,
            'takeoff_efficiency': 0.75,
            'flight_efficiency': 0.92
        }

        # 参数边界（合理的物理范围）
        self.param_bounds = {
            'metabolic_base': (500, 3000),  # 能量基础值
            'muscle_efficiency': (0.15, 0.45),  # 肌肉效率15-45%（生理学范围）
            'chain_efficiency': (0.60, 0.95),  # 动力链效率
            'takeoff_efficiency': (0.50, 0.90),  # 起跳转换效率
            'flight_efficiency': (0.80, 0.98)  # 飞行效率
        }#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d

        self.training_data = []

    def prepare_training_data(self, problem1_results, problem2_results):
        """准备训练数据"""
        training_samples = []

        # 从问题2的综合数据中提取
        for athlete_id in range(3, 11):
            # 获取该运动员的所有数据
            physique = self.get_athlete_physique(athlete_id, problem2_results)
            technical = self.get_athlete_technical(athlete_id, problem2_results)
            actual_score = self.get_athlete_score(athlete_id, problem2_results)

            if physique and technical and actual_score:
                training_samples.append({
                    'physique': physique,
                    'technical': technical,
                    'actual_score': actual_score,
                    'athlete_id': athlete_id
                })

        # 添加运动员1和2的数据
        for athlete_id in [1, 2]:
            data = self.extract_from_problem1(athlete_id, problem1_results)
            if data:
                training_samples.append(data)

        self.training_data = training_samples
        print(f"准备了 {len(training_samples)} 个训练样本")
        return training_samples

    def objective_function(self, params_array):
        """优化的目标函数（最小化预测误差）"""
        # 更新参数
        self.params['metabolic_base'] = params_array[0]
        self.params['muscle_efficiency'] = params_array[1]
        self.params['chain_efficiency'] = params_array[2]
        self.params['takeoff_efficiency'] = params_array[3]
        self.params['flight_efficiency'] = params_array[4]

        # 计算所有训练样本的预测误差
        errors = []
        for sample in self.training_data:
            # 使用能量级联模型预测
            predicted = self.predict_with_params(sample)
            actual = sample['actual_score']

            # 计算误差
            error = (predicted - actual) ** 2
            errors.append(error)

        # 返回均方误差
        mse = np.mean(errors)

        # 添加正则化项防止参数过于极端
        regularization = 0.01 * np.sum(np.abs(params_array - self.get_default_params()))

        return mse + regularization

    def get_default_params(self):
        """获取默认参数值（用于正则化）"""
        return np.array([1500, 0.30, 0.80, 0.70, 0.90])

    def optimize_parameters(self):
        """使用差分进化算法优化参数"""#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
        print("\n开始参数优化...")
        print("=" * 60)

        # 准备边界
        bounds = [
            self.param_bounds['metabolic_base'],
            self.param_bounds['muscle_efficiency'],
            self.param_bounds['chain_efficiency'],
            self.param_bounds['takeoff_efficiency'],
            self.param_bounds['flight_efficiency']
        ]

        # 差分进化优化
        result = differential_evolution(
            self.objective_function,
            bounds,
            maxiter=100,
            popsize=15,
            tol=0.001,
            seed=42,
            disp=True
        )

        # 更新参数
        optimized_params = result.x
        self.params['metabolic_base'] = optimized_params[0]
        self.params['muscle_efficiency'] = optimized_params[1]
        self.params['chain_efficiency'] = optimized_params[2]
        self.params['takeoff_efficiency'] = optimized_params[3]
        self.params['flight_efficiency'] = optimized_params[4]

        print("\n优化后的参数:")
        for key, value in self.params.items():
            print(f"  {key}: {value:.4f}")

        # 计算训练集上的性能
        self.evaluate_on_training_set()

        return self.params

    def evaluate_on_training_set(self):
        """评估模型在训练集上的表现"""
        predictions = []
        actuals = []

        for sample in self.training_data:
            pred = self.predict_with_params(sample)
            predictions.append(pred)
            actuals.append(sample['actual_score'])

        # 计算指标
        mse = mean_squared_error(actuals, predictions)
        rmse = np.sqrt(mse)
        r2 = r2_score(actuals, predictions)
        mae = np.mean(np.abs(np.array(predictions) - np.array(actuals)))

        print(f"\n模型性能:")
        print(f"  RMSE: {rmse:.4f} 米")
        print(f"  MAE:  {mae:.4f} 米")
        print(f"  R²:   {r2:.4f}")

        # 打印每个样本的预测
        print("\n详细预测结果:")
        for i, sample in enumerate(self.training_data):
            print(f"  运动员{sample['athlete_id']}: "
                  f"实际={actuals[i]:.2f}m, "
                  f"预测={predictions[i]:.2f}m, "
                  f"误差={predictions[i] - actuals[i]:.3f}m")

        return {'rmse': rmse, 'mae': mae, 'r2': r2}

    def sensitivity_analysis(self):
        """参数敏感性分析"""
        print("\n参数敏感性分析:")
        print("=" * 60)

        base_predictions = []
        for sample in self.training_data:
            base_predictions.append(self.predict_with_params(sample))

        sensitivities = {}

        for param_name in self.params.keys():
            original_value = self.params[param_name]

            # 增加10%
            self.params[param_name] = original_value * 1.1
            increased_predictions = [self.predict_with_params(s) for s in self.training_data]

            # 减少10%
            self.params[param_name] = original_value * 0.9
            decreased_predictions = [self.predict_with_params(s) for s in self.training_data]

            # 恢复原值
            self.params[param_name] = original_value

            # 计算敏感性
            sensitivity = np.mean(np.abs(np.array(increased_predictions) -
                                         np.array(decreased_predictions))) / (0.2 * original_value)
            sensitivities[param_name] = sensitivity

        # 排序并打印
        sorted_sensitivities = sorted(sensitivities.items(), key=lambda x: x[1], reverse=True)
        for param, sens in sorted_sensitivities:
            print(f"  {param}: 敏感度 = {sens:.6f}")

        return sensitivities

    def predict_with_params(self, sample):
        """使用当前参数预测"""
        # 这里调用你原来的能量级联计算逻辑
        # 简化示例：
        E_metabolic = self.params['metabolic_base'] * (sample['physique'].get('肌肉率 (%)', 45) / 45)
        E_muscle = E_metabolic * self.params['muscle_efficiency']
        E_chain = E_muscle * self.params['chain_efficiency']
        E_takeoff = E_chain * self.params['takeoff_efficiency']
        E_flight = E_takeoff * self.params['flight_efficiency']

        # 简化的能量到距离转换
        distance = np.sqrt(E_flight / 1000) * 0.8  # 简化公式
        return np.clip(distance, 0.8, 2.5)

    def cross_validate(self, k=5):
        """K折交叉验证"""
        from sklearn.model_selection import KFold

        print(f"\n{k}折交叉验证:")
        print("=" * 60)

        kf = KFold(n_splits=min(k, len(self.training_data)), shuffle=True, random_state=42)
        cv_scores = []

        for fold, (train_idx, test_idx) in enumerate(kf.split(self.training_data)):
            # 分割数据
            train_samples = [self.training_data[i] for i in train_idx]
            test_samples = [self.training_data[i] for i in test_idx]

            # 在训练集上优化参数（简化版）
            self.training_data = train_samples
            self.optimize_parameters()

            # 在测试集上评估
            test_predictions = []
            test_actuals = []
            for sample in test_samples:
                test_predictions.append(self.predict_with_params(sample))
                test_actuals.append(sample['actual_score'])

            rmse = np.sqrt(mean_squared_error(test_actuals, test_predictions))
            cv_scores.append(rmse)
            print(f"  Fold {fold + 1}: RMSE = {rmse:.4f}")

        print(f"\n平均RMSE: {np.mean(cv_scores):.4f} (±{np.std(cv_scores):.4f})")

        return cv_scores
