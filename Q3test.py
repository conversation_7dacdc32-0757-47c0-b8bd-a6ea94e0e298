import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 解决中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
class Athlete11AnalyzerFixed:
    def __init__(self, excel_path):
        self.frame_rate = 30.0
        self.data = pd.read_excel(excel_path)
        print(f"数据读取成功: {len(self.data)}帧")

    def detect_takeoff_landing_improved(self):
        """改进的起跳落地检测"""

        # 使用足部Y坐标的变化来检测起跳
        foot_y_smooth = pd.Series(self.foot_y).rolling(window=5, center=True, min_periods=1).mean()

        # 计算垂直速度
        vy_foot = np.gradient(foot_y_smooth)

        # 找到足部开始明显上升的点（起跳）
        # 起跳特征：垂直速度突然变负（向上）
        for i in range(50, len(vy_foot) - 50):
            if vy_foot[i] < -5:  # 明显的向上运动#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
                # 检查之前是否相对静止
                if np.std(foot_y_smooth[i - 20:i]) < 10:
                    # 检查之后是否持续上升
                    if np.mean(vy_foot[i:i + 10]) < -2:
                        self.takeoff_frame = i
                        break

        # 如果没找到，使用备用方法
        if not hasattr(self, 'takeoff_frame'):
            # 寻找水平位移开始增加的点
            x_diff = np.diff(self.foot_x)
            for i in range(50, len(x_diff) - 50):
                if x_diff[i] > 5 and np.mean(x_diff[i:i + 10]) > 3:
                    self.takeoff_frame = i
                    break

        # 如果还是没找到，使用默认值
        if not hasattr(self, 'takeoff_frame'):
            self.takeoff_frame = 120

        # 检测落地：从起跳后0.5秒开始
        search_start = self.takeoff_frame + int(0.5 * self.frame_rate)
        ground_level = np.mean(self.foot_y[:self.takeoff_frame])

        for i in range(search_start, min(len(self.foot_y) - 5, search_start + 60)):
            # 足部回到地面附近
            if abs(self.foot_y[i] - ground_level) < 15:
                # 水平速度显著降低
                if i > self.takeoff_frame + 20:
                    x_velocity = np.diff(self.foot_x[i - 5:i + 5]).mean()
                    if abs(x_velocity) < 5:
                        self.landing_frame = i
                        break

        if not hasattr(self, 'landing_frame'):
            self.landing_frame = self.takeoff_frame + 25  # 约0.83秒滞空时间

    def calculate_jump_distance_corrected(self):
        """修正后的距离计算"""

        # 使用足部最前端位置
        takeoff_x = self.foot_x[self.takeoff_frame]

        # 落地点应该是足部最后接触地面的位置（最远点）
        landing_window = range(self.landing_frame - 5, min(self.landing_frame + 10, len(self.foot_x)))
        landing_x = max([self.foot_x[i] for i in landing_window])#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d

        pixel_distance = abs(landing_x - takeoff_x)

        # 基于其他运动员数据的像素-米转换
        # 运动者1: 438像素 ≈ 1.58米
        # 运动者2: 318像素 ≈ 1.15米
        pixel_to_meter = 0.00361

        self.jump_distance_meters = pixel_distance * pixel_to_meter

        # 计算修正后的起跳参数
        if self.takeoff_frame < len(self.vx):
            # 确保速度方向正确
            self.takeoff_vx = abs(self.vx[self.takeoff_frame])
            self.takeoff_vy = -abs(self.vy[self.takeoff_frame])  # 向上为负
            self.takeoff_speed = np.sqrt(self.takeoff_vx ** 2 + self.takeoff_vy ** 2)
            self.takeoff_angle = np.degrees(np.arctan2(abs(self.takeoff_vy), self.takeoff_vx))

        return self.jump_distance_meters

    def analyze_jump_complete(self):
        """完整分析流程"""

        # 数据平滑
        self.smooth_data = self.data.copy()
        for col in self.data.columns:
            if '_X' in col or '_Y' in col:
                if len(self.data[col]) > 5:
                    self.smooth_data[col] = self.data[col].rolling(
                        window=5, center=True, min_periods=1
                    ).mean()

        # 计算重心和足部位置
        self.calculate_center_of_mass()
        self.calculate_foot_position()

        # 使用改进的检测方法
        self.detect_takeoff_landing_improved()

        # 计算距离
        distance = self.calculate_jump_distance_corrected()

        # 输出结果
        print("\n运动者11 跳远分析结果（修正版）")
        print("=" * 50)
        print(f"起跳帧: 第{self.takeoff_frame}帧 ({self.takeoff_frame / self.frame_rate:.2f}秒)")
        print(f"落地帧: 第{self.landing_frame}帧 ({self.landing_frame / self.frame_rate:.2f}秒)")
        print(f"滞空时间: {(self.landing_frame - self.takeoff_frame) / self.frame_rate:.2f}秒")
        print(f"水平位移: {abs(self.foot_x[self.landing_frame] - self.foot_x[self.takeoff_frame]):.1f}像素")

        if hasattr(self, 'takeoff_speed'):
            print(f"\n起跳参数:")
            print(f"  起跳速度: {self.takeoff_speed:.1f}像素/秒")
            print(f"  起跳角度: {self.takeoff_angle:.1f}度")

        print(f"\n预测跳远成绩: {distance:.2f}米")

        return distance

    def calculate_center_of_mass(self):
        """计算重心（保持原有方法）"""
        self.com_x = []
        self.com_y = []

        for idx, row in self.smooth_data.iterrows():
            key_points = [11, 12, 23, 24]

            x_coords = [row[f'{p}_X'] for p in key_points
                        if f'{p}_X' in row.index and not pd.isna(row[f'{p}_X'])]
            y_coords = [row[f'{p}_Y'] for p in key_points
                        if f'{p}_Y' in row.index and not pd.isna(row[f'{p}_Y'])]

            if x_coords and y_coords:
                self.com_x.append(np.mean(x_coords))
                self.com_y.append(np.mean(y_coords))
            else:
                self.com_x.append(0)
                self.com_y.append(0)

        self.com_x = np.array(self.com_x)
        self.com_y = np.array(self.com_y)

        dt = 1.0 / self.frame_rate
        self.vx = np.gradient(self.com_x, dt)
        self.vy = np.gradient(self.com_y, dt)

    def calculate_foot_position(self):
        """计算足部位置（保持原有方法）"""
        self.foot_x = []
        self.foot_y = []

        for idx, row in self.smooth_data.iterrows():
            foot_points = [29, 30, 31, 32]

            x_coords = [row[f'{p}_X'] for p in foot_points
                        if f'{p}_X' in row.index and not pd.isna(row[f'{p}_X'])]
            y_coords = [row[f'{p}_Y'] for p in foot_points
                        if f'{p}_Y' in row.index and not pd.isna(row[f'{p}_Y'])]

            if x_coords:
                self.foot_x.append(np.mean(x_coords))
            else:
                self.foot_x.append(0)

            if y_coords:
                self.foot_y.append(min(y_coords))
            else:
                self.foot_y.append(0)

        self.foot_x = np.array(self.foot_x)
        self.foot_y = np.array(self.foot_y)


# 执行修正后的分析
def analyze_athlete_11_fixed():
    excel_path = r"C:\Users\<USER>\Desktop\E题\附件\附件5\运动者11的跳远位置信息.xlsx"

    analyzer = Athlete11AnalyzerFixed(excel_path)
    distance = analyzer.analyze_jump_complete()

    result = {
        'measured_distance': distance,
        'takeoff_frame': analyzer.takeoff_frame,
        'landing_frame': analyzer.landing_frame,
        'flight_time': (analyzer.landing_frame - analyzer.takeoff_frame) / analyzer.frame_rate,
        'takeoff_speed': analyzer.takeoff_speed if hasattr(analyzer, 'takeoff_speed') else 350,
        'takeoff_angle': analyzer.takeoff_angle if hasattr(analyzer, 'takeoff_angle') else 28
    }

    print("\n" + "=" * 50)
    print("最终分析结果")
    print("=" * 50)
    print(f"运动者11实测成绩: {result['measured_distance']:.2f}米")
    print(f"（该成绩将用于验证预测模型的准确性）")

    return result


if __name__ == "__main__":
    result = analyze_athlete_11_fixed()