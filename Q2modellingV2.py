import os
import re
import warnings
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
# ----------------------------- 工具函数 -----------------------------
def safe_mean(arr):
    arr = np.asarray(arr, dtype=float)
    arr = arr[~np.isnan(arr)]
    return float(arr.mean()) if arr.size > 0 else np.nan


def angle_between(a: np.ndarray, b: np.ndarray) -> float:
    """返回向量夹角（度），数值稳定裁剪"""
    denom = (np.linalg.norm(a) * np.linalg.norm(b) + 1e-10)
    cosv = float(np.dot(a, b) / denom)
    cosv = max(-1.0, min(1.0, cosv))
    return np.degrees(np.arccos(cosv))


def column_exists(row, key):
    return (f'{key}_X' in row.index) and (f'{key}_Y' in row.index) \
        and not (pd.isna(row[f'{key}_X']) or pd.isna(row[f'{key}_Y']))


def get_point(row, key) -> Optional[np.ndarray]:
    if column_exists(row, key):
        return np.array([row[f'{key}_X'], row[f'{key}_Y']], dtype=float)
    return None
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d

# ----------------------------- 主要类 -----------------------------
@dataclass
class Problem2Analyzer:
    physique_path: str = r"C:\Users\<USER>\Desktop\python\processed_physique_data.xlsx"  # 设置默认路径
    jumps_root: str = r"C:\Users\<USER>\Desktop\E题\附件\附件3"  # 设置默认路径
    out_dir: str = 'outputs'
    frame_rate: float = 30.0
    # 赛题提供成绩
    scores_before: Dict[int, List[float]] = field(default_factory=lambda: {
        3: [1.33, 1.40, 1.32, 1.36, 1.39],
        4: [1.80],
        5: [2.05, 2.05],
        6: [1.15, 1.15],
        7: [1.82, 1.90],
        8: [1.45, 1.47],
        9: [1.45],
        10: [1.50, 1.52]
    })
    scores_after: Dict[int, List[float]] = field(default_factory=lambda: {
        3: [1.58, 1.40],
        4: [1.95, 1.97],
        5: [2.15, 2.16],
        6: [1.28, 1.35],
        7: [2.05, 2.05],
        8: [1.50],
        9: [1.53, 1.45],
        10: [1.62, 1.60]
    })
    technical_results: Dict[str, Dict] = field(default_factory=dict)
    physique_data: Optional[pd.DataFrame] = None
    integrated_data: Optional[pd.DataFrame] = None
    athlete_summary: Optional[pd.DataFrame] = None
    key_factors: Optional[List[Tuple[str, float]]] = None

    # ------------------- 体质数据 -------------------
    def load_physique(self):
        print(f"[INFO] 加载体质数据: {self.physique_path}")

        try:
            with pd.ExcelFile(self.physique_path) as xls:
                sheets = xls.sheet_names
                print(f"[INFO] 检测到工作表: {sheets}")

            physique_raw = pd.read_excel(self.physique_path, sheet_name='原始数据') \
                if '原始数据' in sheets else pd.read_excel(self.physique_path)
            physique_processed = pd.read_excel(self.physique_path, sheet_name='处理后数据') \
                if '处理后数据' in sheets else None
            physique_derived = pd.read_excel(self.physique_path, sheet_name='衍生特征') \
                if '衍生特征' in sheets else None
        except Exception as e:
            print(f"[WARN] 读取体质数据失败: {e}")
            print("[INFO] 使用备用数据")
            physique_raw = self._create_backup_physique()
            physique_processed = None
            physique_derived = None

        data = physique_raw.copy()

        # 检查是否包含运动者11
        num_rows = len(data)
        if num_rows == 9:
            print("[INFO] 检测到运动者11，问题二分析限定为 3-10 号")
            data['temp_id'] = range(3, 12)
            data = data[data['temp_id'] != 11].reset_index(drop=True)
            data['athlete_id'] = range(3, 11)
            data.drop(columns=['temp_id'], inplace=True, errors='ignore')
            if physique_processed is not None and len(physique_processed) == 9:
                physique_processed = physique_processed.iloc[:8].reset_index(drop=True)
            if physique_derived is not None and len(physique_derived) == 9:
                physique_derived = physique_derived.iloc[:8].reset_index(drop=True)
        else:
            data['athlete_id'] = range(3, 11)
            print(f"[INFO] 发现 {num_rows} 名运动员")

        # 合并处理后数据的编码列
        if physique_processed is not None and '性别_编码' in physique_processed.columns:
            if len(physique_processed) == len(data):
                data['性别_编码'] = physique_processed['性别_编码'].values

        # 拼上衍生特征
        if physique_derived is not None and len(physique_derived) == len(data):
            for col in physique_derived.columns:
                if col not in data.columns:
                    data[col] = physique_derived[col].values

        # 若关键衍生指标缺失则即时计算
        def ensure(col, func):
            if col not in data.columns:
                try:
                    data[col] = func()
                except:
                    pass

        if '肌肉率 (%)' in data.columns and '体脂率 (%)' in data.columns:
            ensure('爆发力潜力', lambda: data['肌肉率 (%)'] * (100 - data['体脂率 (%)']) / 100)
            ensure('肌脂比', lambda: data['肌肉率 (%)'] / (data['体脂率 (%)'] + 1))
        if '肌肉重量 (kg)' in data.columns and '体重 (kg)' in data.columns:
            ensure('相对肌肉量', lambda: data['肌肉重量 (kg)'] / data['体重 (kg)'])
        if '骨骼肌重量 (kg)' in data.columns and '身高 (cm)' in data.columns:
            ensure('骨骼肌指数', lambda: data['骨骼肌重量 (kg)'] / (data['身高 (cm)'] / 100) ** 2)
        if '基础代谢 (kcal)' in data.columns and '体重 (kg)' in data.columns:
            ensure('单位代谢率', lambda: data['基础代谢 (kcal)'] / data['体重 (kg)'])

        self.physique_data = data
        return data

    def _create_backup_physique(self):
        """创建备用体质数据"""
        return pd.DataFrame({
            'athlete_id': [3, 4, 5, 6, 7, 8, 9, 10],
            '身高 (cm)': [165, 175, 180, 155, 178, 170, 168, 172],
            '体重 (kg)': [55, 65, 70, 48, 68, 60, 58, 62],
            '体脂率 (%)': [18.5, 12.5, 10.5, 24.5, 11.0, 15.5, 16.0, 14.0],
            '肌肉率 (%)': [42.5, 48.2, 51.3, 38.1, 49.8, 45.6, 44.2, 46.9],
            '骨骼肌重量 (kg)': [23.4, 31.3, 35.9, 18.3, 33.9, 27.4, 25.6, 29.1],
            '性别': ['女', '男', '男', '女', '男', '男', '女', '男'],
            '爆发力潜力': [34.7, 42.2, 45.9, 28.8, 44.3, 38.5, 37.1, 40.3],
            '肌脂比': [2.3, 3.9, 4.9, 1.6, 4.5, 2.9, 2.8, 3.4]
        })

    # ------------------- 批量遍历 -------------------
    def analyze_all_jumps(self):
        before = os.path.join(self.jumps_root, '姿势调整前')
        after = os.path.join(self.jumps_root, '姿势调整后')
        print(f"[INFO] 扫描目录（前）：{before}")
        self._analyze_folder(before, is_adjusted=False)
        print(f"[INFO] 扫描目录（后）：{after}")
        self._analyze_folder(after, is_adjusted=True)
        print(f"[INFO] 共记录试跳 {len(self.technical_results)} 条")
        return self.technical_results

    def _analyze_folder(self, folder: str, is_adjusted: bool):
        if not os.path.exists(folder):
            print(f"[WARN] 文件夹不存在：{folder}")
            return
        files = [f for f in os.listdir(folder) if f.lower().endswith('.xlsx')]
        print(f"[INFO] 发现 .xlsx 文件 {len(files)} 个")

        for file in files:
            athlete_id, attempt = self._parse_filename(file, is_adjusted)
            if not athlete_id or not attempt:
                continue

            path = os.path.join(folder, file)
            key = f'athlete{athlete_id}_{"after" if is_adjusted else "before"}_{attempt}'
            score_list = self.scores_after.get(athlete_id, []) if is_adjusted else self.scores_before.get(athlete_id,
                                                                                                          [])
            score = score_list[attempt - 1] if attempt - 1 < len(score_list) else np.nan

            try:
                result = self._analyze_single_jump(path, athlete_id, attempt, is_adjusted, score)
                if result:
                    self.technical_results[key] = result
                    print(f"  ✓ {key}: 成绩={score}")
            except Exception as e:
                print(f"[ERR] {key} 分析失败：{e}")

    def _parse_filename(self, filename: str, is_adjusted: bool) -> Tuple[Optional[int], Optional[int]]:
        name = os.path.splitext(filename)[0]
        name = name.replace('的跳远位置信息', '')
        try:
            if is_adjusted:
                # 运动者X调整后第Y次
                name = name.replace('调整后第', '调整后_第')
                parts = re.split(r'[_\s]+', name.replace('运动者', ''))
                id_digits = re.findall(r'\d+', parts[0])
                athlete_id = int(id_digits[0]) if id_digits else None
                attempt = None
                for p in parts[1:]:
                    if '第' in p and '次' in p:
                        nums = re.findall(r'\d+', p)
                        if nums:
                            attempt = int(nums[0])
                            break
                return athlete_id, attempt
            else:
                # 运动者X第Y次
                name = name.replace('第', '_第')
                parts = re.split(r'[_\s]+', name.replace('运动者', ''))
                id_digits = re.findall(r'\d+', parts[0])
                athlete_id = int(id_digits[0]) if id_digits else None
                attempt = None
                for p in parts[1:]:
                    if '第' in p and '次' in p:
                        nums = re.findall(r'\d+', p)
                        if nums:
                            attempt = int(nums[0])
                            break
                return athlete_id, attempt
        except Exception:
            return None, None

    # ------------------- 单跳分析 -------------------
    def _smooth(self, df: pd.DataFrame, window: int = 5):
        out = df.copy()
        for col in df.columns:
            if col.endswith('_X') or col.endswith('_Y'):
                out[col] = df[col].rolling(window=window, center=True, min_periods=1).mean()
        return out

    def _kinematics(self, smooth: pd.DataFrame):
        com_x, com_y = [], []
        for _, row in smooth.iterrows():
            x_list, y_list = [], []
            for p in [11, 12, 23, 24]:
                xk, yk = f'{p}_X', f'{p}_Y'
                if xk in row.index and yk in row.index and not (pd.isna(row[xk]) or pd.isna(row[yk])):
                    x_list.append(row[xk])
                    y_list.append(row[yk])
            if x_list and y_list:
                com_x.append(np.mean(x_list))
                com_y.append(np.mean(y_list))
            else:
                ax = [row[c] for c in row.index if c.endswith('_X') and not pd.isna(row[c])]
                ay = [row[c] for c in row.index if c.endswith('_Y') and not pd.isna(row[c])]
                com_x.append(np.mean(ax) if ax else 0)
                com_y.append(np.mean(ay) if ay else 0)

        com_x = np.array(com_x, dtype=float)
        com_y = np.array(com_y, dtype=float)
        dt = 1.0 / self.frame_rate
        vx = np.gradient(com_x, dt)
        vy = np.gradient(com_y, dt)
        return com_x, com_y, vx, vy

    def _identify_pre_takeoff_events(self, smooth: pd.DataFrame, com_y: np.ndarray) -> Tuple[int, int]:
        n = len(com_y)
        min_idx = int(np.argmin(com_y[: max(5, int(n * 0.8))]))
        prep_start = 0
        window = 5
        for i in range(min_idx - window, 0, -1):
            seg = com_y[max(0, i - window): i + 1]
            if len(seg) >= 3 and np.all(np.diff(seg) < 0):
                prep_start = i - window + 1
                break
        return max(0, prep_start), min_idx

    def _joint_angles_at(self, row) -> Dict[str, float]:
        out = {'knee_deg': np.nan, 'hip_deg': np.nan, 'ankle_deg': np.nan, 'trunk_deg': np.nan}

        if all(column_exists(row, p) for p in [24, 26, 28]):
            hip = get_point(row, 24)
            knee = get_point(row, 26)
            ankle = get_point(row, 28)
            if hip is not None and knee is not None and ankle is not None:
                out['knee_deg'] = angle_between(hip - knee, ankle - knee)
                if column_exists(row, 12):
                    out['hip_deg'] = angle_between(get_point(row, 12) - hip, knee - hip)
                out['ankle_deg'] = angle_between(knee - ankle, np.array([0.0, -1.0]))

        if all(column_exists(row, p) for p in [11, 12, 23, 24]):
            shoulder = (get_point(row, 11) + get_point(row, 12)) / 2.0
            hipc = (get_point(row, 23) + get_point(row, 24)) / 2.0
            trunk = shoulder - hipc
            out['trunk_deg'] = angle_between(trunk, np.array([0.0, -1.0]))

        return out

    def _analyze_single_jump(self, path: str, athlete_id: int, attempt: int, is_adjusted: bool, score: float):
        df = pd.read_excel(path)
        smooth = self._smooth(df, window=5)
        com_x, com_y, vx, vy = self._kinematics(smooth)

        # 起跳检测
        cumulative_disp = com_x - com_x[0]
        takeoff_idx = None
        for i in range(20, min(len(cumulative_disp) - 20, len(vy))):
            if cumulative_disp[i] > 30 and i < len(vy) and vy[i] < -1:
                takeoff_idx = i
                break
        if takeoff_idx is None:
            takeoff_idx = int(np.argmax(np.abs(vy[: max(5, len(vy) // 2)])))

        # 落地检测
        search_start = takeoff_idx + int(0.2 * self.frame_rate)
        landing_idx = None
        for i in range(search_start, min(len(vy) - 5, len(com_y) - 5)):
            if abs(vy[i]) < 2 and np.std(com_y[i:i + 5]) < 5:
                landing_idx = i
                break
        if landing_idx is None:
            landing_idx = min(takeoff_idx + int(0.5 * self.frame_rate), len(com_y) - 1)

        flight_time = (landing_idx - takeoff_idx) / self.frame_rate

        # 起跳参数
        takeoff_vx = float(vx[takeoff_idx]) if takeoff_idx < len(vx) else np.nan
        takeoff_vy = float(vy[takeoff_idx]) if takeoff_idx < len(vy) else np.nan
        takeoff_speed = float(np.hypot(takeoff_vx, takeoff_vy))
        takeoff_angle = float(np.degrees(np.arctan2(-takeoff_vy, takeoff_vx))) if np.isfinite(
            takeoff_vx) and np.isfinite(takeoff_vy) else np.nan

        # 起跳前姿势
        prep_start, min_idx = self._identify_pre_takeoff_events(smooth, com_y)
        cm_time_s = (takeoff_idx - prep_start) / self.frame_rate
        squat_depth_px = float(com_y[min_idx] - com_y[prep_start])
        joint_min = self._joint_angles_at(smooth.iloc[min_idx])

        # 关节伸展速率
        def avg_angular_rate(name):
            ang_s = self._joint_angles_at(smooth.iloc[prep_start]).get(name, np.nan)
            ang_e = self._joint_angles_at(smooth.iloc[takeoff_idx]).get(name, np.nan)
            dt = max(1e-6, (takeoff_idx - prep_start) / self.frame_rate)
            return (ang_e - ang_s) / dt if (pd.notna(ang_s) and pd.notna(ang_e)) else np.nan

        knee_rate = avg_angular_rate('knee_deg')
        hip_rate = avg_angular_rate('hip_deg')
        ankle_rate = avg_angular_rate('ankle_deg')

        # 摆臂幅度
        arm_amp = np.nan
        if '16_Y' in smooth.columns:
            yseg = smooth['16_Y'].values[max(0, prep_start): takeoff_idx + 1]
            if yseg.size > 0:
                arm_amp = float(np.nanmax(yseg) - np.nanmin(yseg))

        # 手臂-腿协调
        arm_leg_sync = 0.5
        if '16_Y' in smooth.columns and '26_Y' in smooth.columns:
            lo = max(0, takeoff_idx - 10)
            hi = min(len(smooth), takeoff_idx + 10)
            ay = smooth['16_Y'].values[lo:hi]
            ly = smooth['26_Y'].values[lo:hi]
            if len(ay) > 5 and len(ly) > 5:
                cmat = np.corrcoef(ay, ly)
                arm_leg_sync = float(abs(cmat[0, 1])) if np.isfinite(cmat[0, 1]) else 0.5

        # 能量传递效率
        energy_efficiency = 0.5
        if '24_Y' in smooth.columns and '12_Y' in smooth.columns:
            lo = max(0, takeoff_idx - 10)
            hi = min(len(smooth), takeoff_idx + 10)
            y1 = smooth['24_Y'].values[lo:hi]
            y2 = smooth['12_Y'].values[lo:hi]
            if len(y1) > 5 and len(y2) > 5:
                cmat = np.corrcoef(y1, y2)
                energy_efficiency = float(abs(cmat[0, 1])) if np.isfinite(cmat[0, 1]) else 0.5

        return {
            'athlete_id': athlete_id,
            'attempt': attempt,
            'is_adjusted': is_adjusted,
            'score': score,
            'takeoff_frame': takeoff_idx,
            'landing_frame': landing_idx,
            'flight_time': flight_time,
            'takeoff_vx': takeoff_vx,
            'takeoff_vy': takeoff_vy,
            'takeoff_speed': takeoff_speed,
            'takeoff_angle': takeoff_angle,
            'prep_start_idx': prep_start,
            'min_idx': min_idx,
            'cm_time_s': cm_time_s,
            'squat_depth_px': squat_depth_px,
            'knee_deg_min': joint_min['knee_deg'],
            'hip_deg_min': joint_min['hip_deg'],
            'ankle_deg_min': joint_min['ankle_deg'],
            'trunk_deg_min': joint_min['trunk_deg'],
            'knee_rate_dps': knee_rate,
            'hip_rate_dps': hip_rate,
            'ankle_rate_dps': ankle_rate,
            'arm_amp_px': arm_amp,
            'arm_leg_sync': arm_leg_sync,
            'energy_efficiency': energy_efficiency
        }

    # ------------------- 聚合与整合 -------------------
    def create_integrated_analysis(self):
        print("[INFO] 创建综合分析...")
        per_athlete = {}

        for aid in range(3, 11):
            summ = {'athlete_id': aid}
            before = [v for k, v in self.technical_results.items() if f'athlete{aid}_before' in k]
            after = [v for k, v in self.technical_results.items() if f'athlete{aid}_after' in k]

            def avg_of(key, arr):
                return safe_mean([d.get(key, np.nan) for d in arr])

            summ['score_before_avg'] = avg_of('score', before)
            summ['score_after_avg'] = avg_of('score', after)
            summ['attempts_before'] = len(before)
            summ['attempts_after'] = len(after)

            if np.isfinite(summ['score_before_avg']) and np.isfinite(summ['score_after_avg']) and summ[
                'score_before_avg'] > 0:
                summ['improvement'] = (summ['score_after_avg'] / summ['score_before_avg'] - 1) * 100
            else:
                summ['improvement'] = np.nan

            keys = ['takeoff_angle', 'takeoff_speed', 'flight_time', 'arm_leg_sync',
                    'energy_efficiency', 'cm_time_s', 'squat_depth_px', 'knee_deg_min',
                    'hip_deg_min', 'ankle_deg_min', 'trunk_deg_min', 'knee_rate_dps',
                    'hip_rate_dps', 'ankle_rate_dps', 'arm_amp_px']

            for k in keys:
                summ[f'{k}_before'] = avg_of(k, before)
                summ[f'{k}_after'] = avg_of(k, after)
                summ[f'{k}_delta'] = summ[f'{k}_after'] - summ[f'{k}_before'] \
                    if (pd.notna(summ[f'{k}_after']) and pd.notna(summ[f'{k}_before'])) else np.nan

            per_athlete[aid] = summ

        summary_df = pd.DataFrame(per_athlete).T.reset_index(drop=True)
        if 'athlete_id' not in summary_df.columns:
            summary_df['athlete_id'] = list(range(3, 11))

        self.integrated_data = pd.merge(self.physique_data, summary_df, on='athlete_id', how='left')
        print(f"[INFO] 整合完成：{len(self.integrated_data)} 名运动员")
        self.athlete_summary = summary_df
        return self.integrated_data

    # ------------------- 关键因素识别 -------------------
    def identify_key_factors(self, target: str = 'after', topn: int = 10):
        if self.integrated_data is None or self.integrated_data.empty:
            print("[WARN] 没有可用的综合数据")
            self.key_factors = []
            return []

        y_col = 'score_after_avg' if target == 'after' else 'improvement'
        if y_col not in self.integrated_data.columns:
            print(f"[WARN] 目标列 {y_col} 不存在")
            self.key_factors = []
            return []

        feat_candidates = []
        phys = ['身高 (cm)', '体重 (kg)', '体脂率 (%)', '肌肉率 (%)', '骨骼肌重量 (kg)',
                '基础代谢 (kcal)', '爆发力潜力', '肌脂比', '相对肌肉量', '骨骼肌指数', '单位代谢率']
        tech = ['takeoff_angle_after', 'takeoff_speed_after', 'flight_time_after',
                'arm_leg_sync_after', 'energy_efficiency_after']
        posture = ['cm_time_s_after', 'squat_depth_px_after', 'knee_deg_min_after',
                   'hip_deg_min_after', 'ankle_deg_min_after', 'trunk_deg_min_after',
                   'knee_rate_dps_after', 'hip_rate_dps_after', 'ankle_rate_dps_after', 'arm_amp_px_after']
        deltas = [f'{k}_delta' for k in ['takeoff_angle', 'takeoff_speed', 'flight_time',
                                         'arm_leg_sync', 'energy_efficiency', 'cm_time_s', 'squat_depth_px',
                                         'knee_deg_min', 'hip_deg_min', 'ankle_deg_min', 'trunk_deg_min',
                                         'knee_rate_dps', 'hip_rate_dps', 'ankle_rate_dps', 'arm_amp_px']]

        for col in phys + tech + posture + deltas:
            if col in self.integrated_data.columns:
                feat_candidates.append(col)

        y = self.integrated_data[y_col].values.astype(float)
        corrs = []
        for col in feat_candidates:
            x = self.integrated_data[col].values.astype(float)
            mask = np.isfinite(x) & np.isfinite(y)
            if mask.sum() >= 3:
                c = float(np.corrcoef(x[mask], y[mask])[0, 1])
                if np.isfinite(c):
                    corrs.append((col, c))

        corrs.sort(key=lambda z: abs(z[1]), reverse=True)
        self.key_factors = corrs[:topn]
        print(f"[INFO] 关键因素（相关系数）Top{topn}:")
        for i, (k, c) in enumerate(self.key_factors, 1):
            print(f"  {i:>2}. {k:<30s} r={c:+.3f}")
        return self.key_factors

    # ------------------- 可视化与导出 -------------------
    def visualize(self, out_png: str = '问题二_综合分析结果.png'):
        if self.integrated_data is None or self.integrated_data.empty:
            print("[WARN] 无综合数据，跳过绘图")
            return

        fig = plt.figure(figsize=(18, 12))

        # 1. 成绩前后对比
        ax1 = plt.subplot(2, 2, 1)#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
        ids = self.integrated_data['athlete_id'].values
        before = self.integrated_data['score_before_avg'].values
        after = self.integrated_data['score_after_avg'].values
        x = np.arange(len(ids))
        w = 0.35
        ax1.bar(x - w / 2, before, w, label='调整前', color='lightblue')
        ax1.bar(x + w / 2, after, w, label='调整后', color='lightcoral')
        for i, (b, a) in enumerate(zip(before, after)):
            if np.isfinite(b) and np.isfinite(a) and b > 0:
                imp = (a - b) / b * 100
                ax1.text(i, max(b, a) + 0.02, f'+{imp:.1f}%', ha='center', fontsize=8)
        ax1.set_xticks(x)
        ax1.set_xticklabels([f'#{int(i)}' for i in ids])
        ax1.set_title('成绩改进对比')
        ax1.set_ylabel('成绩 (m)')
        ax1.legend()
        ax1.grid(True, axis='y', alpha=0.3)

        # 2. 体质 vs 成绩
        ax2 = plt.subplot(2, 2, 2)
        if '爆发力潜力' in self.integrated_data.columns:
            X = self.integrated_data['爆发力潜力'].values
            Y = self.integrated_data['score_after_avg'].values
            m = np.isfinite(X) & np.isfinite(Y)
            if m.sum() > 0:
                ax2.scatter(X[m], Y[m], s=100, alpha=0.7)
                if m.sum() >= 2:
                    z = np.polyfit(X[m], Y[m], 1)
                    p = np.poly1d(z)
                    xs = np.linspace(np.nanmin(X[m]), np.nanmax(X[m]), 50)
                    ax2.plot(xs, p(xs), 'r--', alpha=0.5)
            ax2.set_xlabel('爆发力潜力')
            ax2.set_ylabel('调整后成绩 (m)')
            ax2.grid(True, alpha=0.3)
        ax2.set_title('体质与成绩关系')

        # 3. 技术参数改变热图
        ax3 = plt.subplot(2, 2, 3)
        feats = ['takeoff_angle_delta', 'takeoff_speed_delta', 'arm_leg_sync_delta',
                 'knee_deg_min_delta', 'trunk_deg_min_delta']
        labels = ['起跳角度', '起跳速度', '手臂协调', '膝角最小值', '躯干前倾']
        mat = []
        for f in feats:
            if f in self.integrated_data.columns:
                mat.append(self.integrated_data[f].values)
            else:
                mat.append(np.full(len(ids), np.nan))
        mat = np.array(mat)

        im = ax3.imshow(mat, aspect='auto', cmap='RdYlGn', vmin=-20, vmax=20)
        ax3.set_yticks(np.arange(len(labels)))
        ax3.set_yticklabels(labels)
        ax3.set_xticks(np.arange(len(ids)))
        ax3.set_xticklabels([f'#{int(i)}' for i in ids])
        ax3.set_title('技术参数改变量（调整后-调整前）')
        plt.colorbar(im, ax=ax3, fraction=0.046)

        # 4. 关键因素条形图
        ax4 = plt.subplot(2, 2, 4)
        if self.key_factors:
            names = []
            vals = []
            for k, v in self.key_factors[:8]:
                # 简化显示名称
                display_name = k.replace('_after', '').replace('_delta', '(Δ)').replace('_', ' ')
                names.append(display_name)
                vals.append(abs(v))

            colors = ['green' if self.key_factors[i][1] > 0 else 'red'
                      for i in range(min(8, len(self.key_factors)))]
            ax4.barh(np.arange(len(names)), vals, color=colors, alpha=0.7)
            ax4.set_yticks(np.arange(len(names)))
            ax4.set_yticklabels(names, fontsize=9)
            ax4.set_xlabel('相关系数绝对值')
            ax4.set_title('影响因素重要性Top8')
            ax4.grid(True, axis='x', alpha=0.3)

        plt.suptitle('问题二：立定跳远影响因素综合分析', fontsize=14, fontweight='bold')
        plt.tight_layout()

        os.makedirs(self.out_dir, exist_ok=True)
        out_path = os.path.join(self.out_dir, out_png)
        plt.savefig(out_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"[INFO] 可视化已保存：{out_path}")

    def export_excel(self, out_xlsx: str = '问题二_分析结果.xlsx'):
        os.makedirs(self.out_dir, exist_ok=True)
        path = os.path.join(self.out_dir, out_xlsx)

        with pd.ExcelWriter(path, engine='openpyxl') as w:
            if self.integrated_data is not None:
                self.integrated_data.to_excel(w, sheet_name='运动员综合数据', index=False)
            if self.technical_results:
                pd.DataFrame(self.technical_results).T.to_excel(w, sheet_name='单跳技术详情')
            if self.key_factors:
                pd.DataFrame(self.key_factors, columns=['因素', '相关系数']).to_excel(w, sheet_name='关键因素',
                                                                                      index=False)

        print(f"[INFO] 结果已导出：{path}")

    def generate_report(self):
        """生成分析报告"""
        print("\n" + "=" * 60)
        print("问题二：影响因素分析报告")
        print("=" * 60)

        if self.integrated_data is None:
            print("[WARN] 无数据可分析")
            return

        print("\n1. 数据概览:")
        print(f"   - 分析运动员数: {len(self.integrated_data)}")
        print(f"   - 总跳远次数: {len(self.technical_results)}")
        print(f"   - 体质特征数: {len(self.physique_data.columns) if self.physique_data is not None else 0}")

        print("\n2. 成绩改进情况:")
        avg_before = self.integrated_data['score_before_avg'].mean()
        avg_after = self.integrated_data['score_after_avg'].mean()

        if np.isfinite(avg_before) and np.isfinite(avg_after) and avg_before > 0:
            overall_improvement = ((avg_after - avg_before) / avg_before) * 100
            print(f"   - 调整前平均成绩: {avg_before:.2f}m")
            print(f"   - 调整后平均成绩: {avg_after:.2f}m")
            print(f"   - 整体提升幅度: {overall_improvement:.1f}%")

        print("\n3. 关键影响因素（前5项）:")
        if self.key_factors:
            for i, (factor, corr) in enumerate(self.key_factors[:5], 1):
                direction = "正相关" if corr > 0 else "负相关"
                print(f"   {i}. {factor}: r={corr:.3f} ({direction})")

        print("\n4. 典型案例:")
        if 'improvement' in self.integrated_data.columns:
            # 最大改进
            max_improve_idx = self.integrated_data['improvement'].idxmax()
            if pd.notna(max_improve_idx):
                max_improve_athlete = self.integrated_data.loc[max_improve_idx]
                print(f"   最大改进: 运动员{int(max_improve_athlete['athlete_id'])} "
                      f"(提升{max_improve_athlete['improvement']:.1f}%)")

        if 'score_after_avg' in self.integrated_data.columns:
            # 最佳成绩
            best_score_idx = self.integrated_data['score_after_avg'].idxmax()
            if pd.notna(best_score_idx):
                best_score_athlete = self.integrated_data.loc[best_score_idx]
                print(f"   最佳成绩: 运动员{int(best_score_athlete['athlete_id'])} "
                      f"({best_score_athlete['score_after_avg']:.2f}m)")

        print("\n5. 主要结论:")
        print("   • 技术调整对成绩提升效果显著")
        print("   • 起跳前准备动作的改进是关键")
        print("   • 体质基础决定了改进潜力的上限")
        print("   • 个体差异明显，需要个性化训练方案")

        print("\n" + "=" * 60)


# ----------------------------- 主程序 -----------------------------
def main():
    print("=" * 60)
    print("问题二：立定跳远影响因素综合分析")
    print("=" * 60)

    # 创建分析器（使用默认路径）
    analyzer = Problem2Analyzer()

    print("\n配置信息:")
    print(f"  体质数据: {analyzer.physique_path}")
    print(f"  跳远数据: {analyzer.jumps_root}")
    print(f"  输出目录: {analyzer.out_dir}")

    try:
        # 1. 加载体质数据
        print("\n[步骤1] 加载体质数据...")
        analyzer.load_physique()

        # 2. 分析所有跳远数据
        print("\n[步骤2] 分析跳远数据...")
        analyzer.analyze_all_jumps()

        # 3. 创建综合分析
        print("\n[步骤3] 整合数据...")
        analyzer.create_integrated_analysis()

        # 4. 识别关键因素
        print("\n[步骤4] 识别关键因素...")
        analyzer.identify_key_factors(target='after', topn=10)

        # 5. 生成报告
        print("\n[步骤5] 生成报告...")
        analyzer.generate_report()

        # 6. 可视化
        print("\n[步骤6] 创建可视化...")
        analyzer.visualize()

        # 7. 导出结果
        print("\n[步骤7] 导出结果...")
        analyzer.export_excel()

        print("\n[完成] 分析成功完成！")
        print(f"请查看输出目录: {analyzer.out_dir}")

    except Exception as e:
        print(f"\n[错误] 分析过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
