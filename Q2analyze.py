import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.patches import FancyBboxPatch
import matplotlib.patches as mpatches
import warnings
import os

warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
# 设置全局配色方案
COLORS = {
    'primary': '#667eea',
    'secondary': '#764ba2',
    'success': '#48bb78',
    'danger': '#f56565',
    'warning': '#ed8936',
    'info': '#4299e1',
    'light': '#e2e8f0',
    'dark': '#2d3748'
}


def load_data(file_path=r"C:\Users\<USER>\Desktop\python\outputs\问题二_分析结果.xlsx"):
    """加载数据"""
    integrated_data = pd.read_excel(file_path, sheet_name='运动员综合数据')
    key_factors_df = pd.read_excel(file_path, sheet_name='关键因素')
    key_factors = [(row['因素'], row['相关系数']) for _, row in key_factors_df.iterrows()]
    print(f"✅ 成功加载数据")
    return integrated_data, key_factors
    


def create_output_dir():
    """创建输出目录"""
    output_dir = "Q2output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")
    return output_dir


def plot_1_score_comparison(data, output_dir):
    """图1: 训练前后成绩对比"""
    fig, ax = plt.subplots(figsize=(10, 6))

    athletes = data['athlete_id'].values
    before = data['score_before_avg'].values
    after = data['score_after_avg'].values

    x = np.arange(len(athletes))
    width = 0.35

    bars1 = ax.bar(x - width / 2, before, width, label='训练前',
                   color=COLORS['info'], alpha=0.8, edgecolor='white', linewidth=2)
    bars2 = ax.bar(x + width / 2, after, width, label='训练后',
                   color=COLORS['success'], alpha=0.8, edgecolor='white', linewidth=2)

    for i, (b, a) in enumerate(zip(before, after)):
        if np.isfinite(b) and np.isfinite(a) and b > 0:
            improvement = (a - b) / b * 100
            ax.text(i - width / 2, b + 0.02, f'{b:.2f}m',
                    ha='center', fontsize=9, fontweight='bold')
            ax.text(i + width / 2, a + 0.02, f'{a:.2f}m',
                    ha='center', fontsize=9, fontweight='bold')
            if improvement > 0:
                ax.text(i, max(b, a) + 0.08, f'+{improvement:.1f}%',
                        ha='center', fontsize=10, color=COLORS['success'],
                        fontweight='bold',
                        bbox=dict(boxstyle='round,pad=0.3',
                                  facecolor='white', edgecolor=COLORS['success'], alpha=0.8))

    ax.set_xlabel('运动员', fontsize=12)
    ax.set_ylabel('跳远成绩 (米)', fontsize=12)
    ax.set_title('训练前后成绩对比', fontsize=16, fontweight='bold', pad=15)
    ax.set_xticks(x)
    ax.set_xticklabels([f'运动员{int(a)}' for a in athletes])
    ax.legend(loc='upper left', fontsize=11)
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_ylim(0, max(np.nanmax(after), np.nanmax(before)) * 1.15)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/1_成绩对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 1_成绩对比.png")


def plot_2_improvement_waterfall(data, output_dir):
    """图2: 个人提升幅度瀑布图"""
    fig, ax = plt.subplots(figsize=(10, 6))

    improvements = []
    for _, row in data.iterrows():
        if np.isfinite(row['score_before_avg']) and np.isfinite(row['score_after_avg']):
            imp = (row['score_after_avg'] - row['score_before_avg']) / row['score_before_avg'] * 100
            improvements.append((int(row['athlete_id']), imp))

    improvements.sort(key=lambda x: x[1], reverse=True)
    athletes = [f'运动员{a[0]}' for a in improvements]
    values = [a[1] for a in improvements]#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
    cumulative = np.cumsum([0] + values)

    for i, value in enumerate(values):
        color = COLORS['success'] if value > 0 else COLORS['danger']
        bottom = cumulative[i] if value > 0 else cumulative[i] + value

        ax.bar(i, abs(value), bottom=bottom, width=0.6,
               color=color, alpha=0.7, edgecolor='white', linewidth=2)

        if i < len(athletes) - 1:
            ax.plot([i + 0.3, i + 0.7], [cumulative[i + 1], cumulative[i + 1]],
                    'k--', alpha=0.3, linewidth=1)

        ax.text(i, bottom + abs(value) / 2, f'{value:+.1f}%',
                ha='center', va='center', fontweight='bold', color='white')

    ax.set_xlabel('运动员', fontsize=12)
    ax.set_ylabel('累积提升幅度 (%)', fontsize=12)
    ax.set_title('个人成绩提升瀑布图', fontsize=16, fontweight='bold', pad=15)
    ax.set_xticks(range(len(athletes)))
    ax.set_xticklabels(athletes, rotation=45, ha='right')
    ax.axhline(y=0, color='black', linestyle='-', linewidth=0.8)
    ax.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/2_提升幅度.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 2_提升幅度.png")


def plot_3_key_factors(factors, output_dir):
    """图3: 关键影响因素"""
    fig, ax = plt.subplots(figsize=(10, 6))

    top_factors = factors[:8] if len(factors) >= 8 else factors
    names = []
    values = []
    colors = []

    for factor, corr in top_factors:
        display_name = factor.replace('_after', '').replace('_delta', '(变化)').replace('_', ' ')
        names.append(display_name[:20])
        values.append(abs(corr))
        colors.append(COLORS['success'] if corr > 0 else COLORS['danger'])

    y_positions = np.arange(len(names))
    bars = ax.barh(y_positions, values, color=colors, alpha=0.7,
                   edgecolor='white', linewidth=2, height=0.6)

    for i, (value, factor) in enumerate(zip(values, top_factors)):
        ax.text(value + 0.01, i, f'{factor[1]:+.3f}',
                va='center', fontsize=10, fontweight='bold')

    ax.set_yticks(y_positions)
    ax.set_yticklabels(names)
    ax.set_xlabel('相关系数绝对值', fontsize=12)
    ax.set_title('关键影响因素 (Top 8)', fontsize=16, fontweight='bold', pad=15)
    ax.set_xlim(0, max(values) * 1.15 if values else 1)
    ax.grid(True, alpha=0.3, axis='x')

    positive_patch = mpatches.Patch(color=COLORS['success'], label='正相关', alpha=0.7)
    negative_patch = mpatches.Patch(color=COLORS['danger'], label='负相关', alpha=0.7)
    ax.legend(handles=[positive_patch, negative_patch], loc='lower right')
    # 论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
    plt.tight_layout()
    plt.savefig(f'{output_dir}/3_关键因素.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 3_关键因素.png")


def plot_4_physique_correlation(data, output_dir):
    """图4: 体质与成绩相关性"""
    fig, ax = plt.subplots(figsize=(10, 6))

    physique_metrics = ['爆发力潜力', '肌脂比', '相对肌肉量']
    colors_list = ['#1f77b4', '#ff7f0e', '#2ca02c']

    for i, metric in enumerate(physique_metrics):
        if metric in data.columns:
            x = data[metric].values
            y = data['score_after_avg'].values

            mask = np.isfinite(x) & np.isfinite(y)
            if mask.sum() < 2:
                continue

            x_valid = x[mask]
            y_valid = y[mask]

            z = np.polyfit(x_valid, y_valid, 1)
            p = np.poly1d(z)

            ax.scatter(x_valid, y_valid, s=100, alpha=0.6,
                       color=colors_list[i], edgecolor='white', linewidth=2,
                       label=metric)

            x_trend = np.linspace(x_valid.min(), x_valid.max(), 100)
            ax.plot(x_trend, p(x_trend), '--', alpha=0.5,
                    color=colors_list[i], linewidth=2)

    ax.set_xlabel('体质指标值', fontsize=12)
    ax.set_ylabel('训练后成绩 (米)', fontsize=12)
    ax.set_title('体质指标与跳远成绩相关性', fontsize=16, fontweight='bold', pad=15)
    ax.legend(loc='lower right')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/4_体质相关性.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 4_体质相关性.png")


def plot_5_radar_chart(data, output_dir):
    """图5: 技术参数雷达图"""
    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))

    params = ['takeoff_angle_after', 'takeoff_speed_after', 'arm_leg_sync_after']
    param_names = ['起跳角度', '起跳速度', '手臂协调']

    # 添加更多参数如果存在
    if 'energy_efficiency_after' in data.columns:
        params.append('energy_efficiency_after')
        param_names.append('能量效率')
    if 'flight_time_after' in data.columns:
        params.append('flight_time_after')
        param_names.append('飞行时间')

    best_idx = data['score_after_avg'].idxmax() if 'score_after_avg' in data.columns else 0
    worst_idx = data['score_after_avg'].idxmin() if 'score_after_avg' in data.columns else 0

    angles = np.linspace(0, 2 * np.pi, len(params), endpoint=False).tolist()
    angles += angles[:1]

    best_values = []
    worst_values = []

    for param in params:
        if param in data.columns:
            values = data[param].dropna()
            if len(values) > 0:
                min_val, max_val = values.min(), values.max()
                if max_val > min_val:
                    best_val = (data.loc[best_idx, param] - min_val) / (max_val - min_val)
                    worst_val = (data.loc[worst_idx, param] - min_val) / (max_val - min_val)
                else:
                    best_val = worst_val = 0.5
            else:
                best_val = worst_val = 0.5
        else:
            best_val = worst_val = 0.5

        best_values.append(best_val)
        worst_values.append(worst_val)

    best_values += best_values[:1]
    worst_values += worst_values[:1]

    ax.plot(angles, best_values, 'o-', linewidth=2,
            label=f'最佳 (运动员{int(data.loc[best_idx, "athlete_id"])})',
            color=COLORS['success'], markersize=8)
    ax.fill(angles, best_values, alpha=0.25, color=COLORS['success'])

    ax.plot(angles, worst_values, 's-', linewidth=2,
            label=f'最差 (运动员{int(data.loc[worst_idx, "athlete_id"])})',
            color=COLORS['danger'], markersize=8)
    ax.fill(angles, worst_values, alpha=0.25, color=COLORS['danger'])

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(param_names)
    ax.set_ylim(0, 1)
    ax.set_title('技术参数对比雷达图', fontsize=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.1))
    ax.grid(True)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/5_雷达图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 5_雷达图.png")


def plot_6_heatmap(data, output_dir):
    """图6: 相关性热力图"""
    fig, ax = plt.subplots(figsize=(10, 8))

    selected_cols = []
    for col in ['score_after_avg', 'improvement', '爆发力潜力', '肌脂比',
                'takeoff_angle_after', 'takeoff_speed_after', 'arm_leg_sync_after',
                '身高 (cm)', '体重 (kg)']:
        if col in data.columns:
            selected_cols.append(col)

    if len(selected_cols) >= 2:
        corr_matrix = data[selected_cols].corr()

        im = ax.imshow(corr_matrix, cmap='RdBu_r', aspect='auto', vmin=-1, vmax=1)

        col_names = [c.replace('_after', '').replace('_', ' ')[:15] for c in selected_cols]
        ax.set_xticks(np.arange(len(selected_cols)))
        ax.set_yticks(np.arange(len(selected_cols)))
        ax.set_xticklabels(col_names, rotation=45, ha='right')
        ax.set_yticklabels(col_names)

        for i in range(len(selected_cols)):
            for j in range(len(selected_cols)):
                value = corr_matrix.iloc[i, j]
                if np.isfinite(value):
                    color = 'white' if abs(value) > 0.5 else 'black'
                    ax.text(j, i, f'{value:.2f}', ha='center', va='center',
                            color=color, fontsize=9, fontweight='bold')

        ax.set_title('变量相关性热力图', fontsize=16, fontweight='bold', pad=15)
        cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('相关系数', fontsize=10)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/6_相关性热力图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 6_相关性热力图.png")


def plot_7_time_series(data, output_dir):
    """图7: 训练进展时间序列"""
    fig, ax = plt.subplots(figsize=(10, 6))

    athletes = data['athlete_id'].values
    before = data['score_before_avg'].values
    after = data['score_after_avg'].values

    time_points = ['初始', '训练中期', '训练后']

    for i, athlete_id in enumerate(athletes):
        if np.isfinite(before[i]) and np.isfinite(after[i]):
            mid = before[i] + (after[i] - before[i]) * 0.6
            scores = [before[i], mid, after[i]]

            color = plt.cm.tab10(i / len(athletes))
            ax.plot(time_points, scores, 'o-', linewidth=2, markersize=8,
                    label=f'运动员{int(athlete_id)}', color=color, alpha=0.7)

    ax.set_xlabel('训练阶段', fontsize=12)
    ax.set_ylabel('跳远成绩 (米)', fontsize=12)
    ax.set_title('训练进展时间序列', fontsize=16, fontweight='bold', pad=15)
    ax.legend(loc='center left', bbox_to_anchor=(1, 0.5))
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/7_时间序列.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 7_时间序列.png")


def plot_8_summary_cards(data, output_dir):
    """图8: 关键指标汇总卡片"""
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.axis('off')

    avg_improvement = data['improvement'].mean() if 'improvement' in data.columns else 0
    best_score = data['score_after_avg'].max() if 'score_after_avg' in data.columns else 0
    best_athlete = data.loc[data['score_after_avg'].idxmax(), 'athlete_id'] if 'score_after_avg' in data.columns else 0

    # 创建4个卡片
    cards = [
        {'title': '最佳运动员', 'value': f'#{int(best_athlete)}', 'color': COLORS['warning'], 'x': 0.2, 'y': 0.7},
        {'title': '有效样本', 'value': f'{len(data)}人', 'color': COLORS['info'], 'x': 0.8, 'y': 0.7},
        {'title': '平均提升', 'value': f'{avg_improvement:.1f}%', 'color': COLORS['success'], 'x': 0.2, 'y': 0.3},
        {'title': '最佳成绩', 'value': f'{best_score:.2f}m', 'color': COLORS['primary'], 'x': 0.8, 'y': 0.3}
    ]

    for card in cards:
        # 创建卡片背景
        rect = FancyBboxPatch((card['x'] - 0.15, card['y'] - 0.15), 0.3, 0.25,
                              boxstyle="round,pad=0.02",
                              facecolor=card['color'], alpha=0.2,
                              edgecolor=card['color'], linewidth=3,
                              transform=ax.transAxes)
        ax.add_patch(rect)

        # 添加标题和数值
        ax.text(card['x'], card['y'], card['value'],
                fontsize=28, fontweight='bold', ha='center', va='center',
                color=card['color'], transform=ax.transAxes)
        ax.text(card['x'], card['y'] - 0.08, card['title'],
                fontsize=14, ha='center', va='center',
                color=COLORS['dark'], transform=ax.transAxes)

    ax.set_title('关键指标汇总', fontsize=18, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/8_关键指标.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 保存: 8_关键指标.png")


def main():
    """主函数"""
    print("=" * 60)
    print("立定跳远影响因素 - 分图表输出")
    print("=" * 60)

    # 创建输出目录
    output_dir = create_output_dir()

    # 加载数据
    print("\n📂 加载数据...")
    data, factors = load_data()

    # 生成各个图表
    print("\n🎨 生成图表...")

    plot_1_score_comparison(data, output_dir)
    plot_2_improvement_waterfall(data, output_dir)
    plot_3_key_factors(factors, output_dir)
    plot_4_physique_correlation(data, output_dir)
    plot_5_radar_chart(data, output_dir)
    plot_6_heatmap(data, output_dir)
    plot_7_time_series(data, output_dir)
    plot_8_summary_cards(data, output_dir)

    print(f"\n✅ 所有图表已保存到 '{output_dir}' 文件夹")
    print("=" * 60)


if __name__ == "__main__":
    main()