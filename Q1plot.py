import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import savgol_filter
import warnings

warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
class CompleteJumpAnalyzer:
    """完整的立定跳远分析系统，包含基础检测和详细身体分析"""

    def __init__(self, excel_path, athlete_name, actual_score):
        self.athlete_name = athlete_name
        self.actual_score = actual_score
        self.frame_rate = 30.0

        print(f"读取{athlete_name}的数据...")
        self.data = pd.read_excel(excel_path)
        print(f"数据读取成功: {len(self.data)}帧")

        self.results = {}
        self.body_parts_data = {}

    def preprocess_data(self):
        """数据预处理"""
        print("数据预处理...")

        self.smooth_data = self.data.copy()#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
        coordinate_cols = [col for col in self.data.columns if '_X' in col or '_Y' in col]

        for col in coordinate_cols:
            if col in self.data.columns and len(self.data[col]) > 5:
                # 简单移动平均平滑
                window = 5
                self.smooth_data[col] = self.data[col].rolling(window=window, center=True, min_periods=1).mean()

        self._calculate_center_of_mass()
        self._calculate_kinematics()

    def _calculate_center_of_mass(self):
        """计算重心"""
        self.com_x = []
        self.com_y = []

        for idx, row in self.smooth_data.iterrows():
            # 使用主要躯干节点计算重心
            key_points = [11, 12, 23, 24]  # 肩膀和髋部

            x_coords = [row[f'{p}_X'] for p in key_points if f'{p}_X' in row.index]
            y_coords = [row[f'{p}_Y'] for p in key_points if f'{p}_Y' in row.index]

            if x_coords and y_coords:#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d
                self.com_x.append(np.mean(x_coords))
                self.com_y.append(np.mean(y_coords))
            else:
                all_x = [row[col] for col in row.index if '_X' in col and not pd.isna(row[col])]
                all_y = [row[col] for col in row.index if '_Y' in col and not pd.isna(row[col])]
                self.com_x.append(np.mean(all_x) if all_x else 0)
                self.com_y.append(np.mean(all_y) if all_y else 0)

        self.com_x = np.array(self.com_x)
        self.com_y = np.array(self.com_y)

    def _calculate_kinematics(self):
        """计算运动学参数"""
        dt = 1.0 / self.frame_rate

        self.vx = np.gradient(self.com_x, dt)
        self.vy = np.gradient(self.com_y, dt)

        # 足部追踪
        self.foot_min_y = []
        self.foot_center_x = []

        for idx, row in self.smooth_data.iterrows():
            foot_points = [29, 30, 31, 32]
            foot_y_coords = [row[f'{p}_Y'] for p in foot_points
                             if f'{p}_Y' in row.index and not pd.isna(row[f'{p}_Y'])]
            foot_x_coords = [row[f'{p}_X'] for p in foot_points
                             if f'{p}_X' in row.index and not pd.isna(row[f'{p}_X'])]

            if foot_y_coords:
                self.foot_min_y.append(min(foot_y_coords))
            else:
                self.foot_min_y.append(0)

            if foot_x_coords:
                self.foot_center_x.append(np.mean(foot_x_coords))
            else:
                self.foot_center_x.append(0)

        self.foot_min_y = np.array(self.foot_min_y)
        self.foot_center_x = np.array(self.foot_center_x)

    def detect_takeoff_and_landing(self):
        """检测起跳和落地时刻"""
        print("\n检测起跳和落地时刻...")#论文写作过程中遗留数个致命问题，无关代码，该问题解决方式仅在官网授权售后群答疑.。售后群提供降重服务，因倒卖导致论文无法降重进而导致通报情况概不负责。感谢理解资料助攻购买链接+说明https://docs.qq.com/doc/p/7df237931b61c9b7b0f2fdc61e3012273652064d

        # 分析横向移动模式
        cumulative_displacement = self.foot_center_x - self.foot_center_x[0]

        # 寻找第一次大幅度正向移动
        movement_threshold = 30
        significant_moves = []

        for i in range(1, len(cumulative_displacement)):
            if cumulative_displacement[i] > movement_threshold:
                is_first_time = all(cumulative_displacement[:i] <= movement_threshold)

                if is_first_time:
                    if i >= 5:
                        recent_trend = cumulative_displacement[i] - cumulative_displacement[i - 5]
                        is_continuous = recent_trend > 10
                    else:
                        is_continuous = True

                    if is_continuous:
                        significant_moves.append(i)

        # 确定起跳时刻
        if significant_moves:
            first_major_move = significant_moves[0]
            search_start = max(0, first_major_move - 10)
            search_end = min(len(self.com_y) - 15, first_major_move + 5)

            best_takeoff = None
            max_combined_score = 0

            for i in range(search_start, search_end):
                # 评分标准
                score = 0

                # 高度增益
                if i + 15 < len(self.com_y):
                    height_gain = self.com_y[i] - np.min(self.com_y[i:i + 15])
                    score += min(30, height_gain * 1.5)

                # 足部离地
                ground_baseline = np.mean(self.foot_min_y[:max(10, i)]) if i > 0 else np.mean(self.foot_min_y[:10])
                foot_lift = ground_baseline - self.foot_min_y[i]
                if foot_lift > 0:
                    score += min(20, foot_lift * 2)

                # 向前速度
                if i < len(self.vx):
                    forward_velocity = self.vx[i]
                    if forward_velocity > 0:
                        score += min(20, forward_velocity)

                # 后续位移
                if i + 20 < len(self.foot_center_x):
                    future_displacement = abs(self.foot_center_x[i + 20] - self.foot_center_x[i])
                    score += min(30, future_displacement * 0.3)

                if score > max_combined_score and score > 40:
                    max_combined_score = score
                    best_takeoff = i

            self.takeoff_frame = best_takeoff if best_takeoff else first_major_move
        else:
            # 备用方法
            min_height_idx = np.argmin(self.com_y[:int(len(self.com_y) * 0.8)])
            self.takeoff_frame = min_height_idx

        # 检测落地时刻
        ground_baseline = np.mean(self.foot_min_y[:self.takeoff_frame])
        min_flight_frames = int(0.25 * self.frame_rate)
        search_start = self.takeoff_frame + min_flight_frames

        for i in range(search_start, len(self.foot_min_y) - 5):
            near_ground = abs(self.foot_min_y[i] - ground_baseline) < 20

            if i >= 10:
                recent_displacement = abs(self.foot_center_x[i] - self.foot_center_x[i - 10])
                movement_stopped = recent_displacement < 15
            else:
                movement_stopped = True

            if i < len(self.vy):
                not_rising = self.vy[i] <= 2.0
            else:
                not_rising = True

            stable_afterwards = True
            if i + 10 < len(self.foot_min_y):
                future_foot_std = np.std(self.foot_min_y[i:i + 10])
                stable_afterwards = future_foot_std < 8

            if near_ground and movement_stopped and not_rising and stable_afterwards:
                self.landing_frame = i
                break
        else:
            estimated_flight_time = 0.5
            self.landing_frame = self.takeoff_frame + int(estimated_flight_time * self.frame_rate)

        self.flight_time = (self.landing_frame - self.takeoff_frame) / self.frame_rate

        print(f"起跳时刻: 第{self.takeoff_frame}帧 ({self.takeoff_frame / self.frame_rate:.3f}秒)")
        print(f"落地时刻: 第{self.landing_frame}帧 ({self.landing_frame / self.frame_rate:.3f}秒)")
        print(f"滞空时间: {self.flight_time:.3f}秒")

        self.results['takeoff_frame'] = self.takeoff_frame
        self.results['landing_frame'] = self.landing_frame
        self.results['flight_time'] = self.flight_time

    def analyze_body_parts_movement(self):
        """分析身体各部位的详细运动过程"""

        print("\n身体各部位详细运动分析")
        print("=" * 50)

        # 定义身体部位
        body_parts = {
            'head': {'points': [0, 1, 2, 3, 4], 'name': '头部'},
            'shoulders': {'points': [11, 12], 'name': '肩部'},
            'elbows': {'points': [13, 14], 'name': '肘部'},
            'wrists': {'points': [15, 16], 'name': '腕部'},
            'hips': {'points': [23, 24], 'name': '髋部'},
            'knees': {'points': [25, 26], 'name': '膝部'},
            'ankles': {'points': [27, 28], 'name': '踝部'},
            'feet': {'points': [29, 30, 31, 32], 'name': '脚部'}
        }

        # 分析每个部位
        for part_key, part_info in body_parts.items():
            self.body_parts_data[part_key] = self._analyze_single_part(
                part_info['points'], part_info['name']
            )

        # 分析关节角度
        self._analyze_joint_angles()

        # 分析滞空阶段细节
        self._analyze_flight_phases()

    def _analyze_single_part(self, points, part_name):
        """分析单个身体部位"""

        # 计算该部位的中心坐标
        x_coords = []
        y_coords = []

        for idx, row in self.smooth_data.iterrows():
            part_x = []
            part_y = []

            for p in points:
                if f'{p}_X' in row.index and not pd.isna(row[f'{p}_X']):
                    part_x.append(row[f'{p}_X'])
                if f'{p}_Y' in row.index and not pd.isna(row[f'{p}_Y']):
                    part_y.append(row[f'{p}_Y'])

            x_coords.append(np.mean(part_x) if part_x else 0)
            y_coords.append(np.mean(part_y) if part_y else 0)

        x_coords = np.array(x_coords)
        y_coords = np.array(y_coords)

        # 计算速度和加速度
        dt = 1.0 / self.frame_rate
        vx = np.gradient(x_coords, dt)
        vy = np.gradient(y_coords, dt)
        ax = np.gradient(vx, dt)
        ay = np.gradient(vy, dt)

        return {
            'x': x_coords,
            'y': y_coords,
            'vx': vx,
            'vy': vy,
            'ax': ax,
            'ay': ay
        }

    def _analyze_joint_angles(self):
        """分析关节角度变化"""

        self.joint_angles = {}

        # 膝关节角度
        knee_angles = []
        for idx, row in self.smooth_data.iterrows():
            if all(f'{p}_X' in row.index for p in [24, 26, 28]):
                hip = np.array([row['24_X'], row['24_Y']])
                knee = np.array([row['26_X'], row['26_Y']])
                ankle = np.array([row['28_X'], row['28_Y']])

                v1 = hip - knee
                v2 = ankle - knee

                cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-10)
                cos_angle = np.clip(cos_angle, -1, 1)
                angle = np.arccos(cos_angle)
                knee_angles.append(np.degrees(angle))
            else:
                knee_angles.append(180)

        self.joint_angles['knee'] = np.array(knee_angles)

        # 躯干倾斜角
        trunk_angles = []
        for idx, row in self.smooth_data.iterrows():
            if all(f'{p}_X' in row.index for p in [11, 12, 23, 24]):
                shoulder_center = np.array([(row['11_X'] + row['12_X']) / 2,
                                            (row['11_Y'] + row['12_Y']) / 2])
                hip_center = np.array([(row['23_X'] + row['24_X']) / 2,
                                       (row['23_Y'] + row['24_Y']) / 2])

                trunk_vector = shoulder_center - hip_center
                vertical_vector = np.array([0, -1])

                cos_angle = np.dot(trunk_vector, vertical_vector) / \
                            (np.linalg.norm(trunk_vector) * np.linalg.norm(vertical_vector) + 1e-10)
                cos_angle = np.clip(cos_angle, -1, 1)
                angle = np.arccos(cos_angle)
                trunk_angles.append(np.degrees(angle))
            else:
                trunk_angles.append(90)

        self.joint_angles['trunk'] = np.array(trunk_angles)

    def _analyze_flight_phases(self):
        """分析滞空阶段的三个子阶段"""

        print("\n滞空阶段详细分析")
        print("-" * 30)

        flight_frames = self.landing_frame - self.takeoff_frame

        # 划分三个阶段
        rise_end = self.takeoff_frame + int(flight_frames * 0.3)
        peak_end = self.takeoff_frame + int(flight_frames * 0.7)

        phases = {
            '上升期': (self.takeoff_frame, rise_end),
            '最高点期': (rise_end, peak_end),
            '下降期': (peak_end, self.landing_frame)
        }

        self.flight_phases = {}

        for phase_name, (start, end) in phases.items():
            print(f"\n{phase_name} (第{start}-{end}帧):")

            # 分析该阶段的特征
            phase_data = {}

            # 重心高度变化
            com_height_change = self.com_y[end] - self.com_y[start]
            phase_data['重心高度变化'] = com_height_change
            print(f"  重心高度变化: {com_height_change:.1f}像素")

            # 膝关节平均角度
            avg_knee_angle = np.mean(self.joint_angles['knee'][start:end + 1])
            phase_data['平均膝角'] = avg_knee_angle
            print(f"  平均膝关节角度: {avg_knee_angle:.1f}°")

            # 躯干平均倾斜
            avg_trunk_angle = np.mean(self.joint_angles['trunk'][start:end + 1])
            phase_data['平均躯干角'] = avg_trunk_angle
            print(f"  平均躯干倾斜: {avg_trunk_angle:.1f}°")

            # 水平速度
            avg_vx = np.mean(self.vx[start:end + 1])
            phase_data['平均水平速度'] = avg_vx
            print(f"  平均水平速度: {avg_vx:.1f}像素/秒")

            self.flight_phases[phase_name] = phase_data

    def create_visualization(self):
        """创建可视化分析图"""

        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle(f'{self.athlete_name} - 立定跳远运动分析', fontsize=14, fontweight='bold')

        # 1. 重心轨迹
        ax = axes[0, 0]
        flight_range = range(self.takeoff_frame, self.landing_frame + 1)

        ax.plot(self.com_x[flight_range], self.com_y[flight_range],
                'b-', linewidth=2, label='重心轨迹')
        ax.scatter(self.com_x[self.takeoff_frame], self.com_y[self.takeoff_frame],
                   c='green', s=100, marker='^', label='起跳', zorder=5)
        ax.scatter(self.com_x[self.landing_frame], self.com_y[self.landing_frame],
                   c='red', s=100, marker='v', label='落地', zorder=5)

        ax.set_xlabel('水平位置 (像素)')
        ax.set_ylabel('垂直位置 (像素)')
        ax.set_title('滞空期重心轨迹')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.invert_yaxis()

        # 2. 关节角度变化
        ax = axes[0, 1]
        time_frames = np.arange(len(self.joint_angles['knee'])) / self.frame_rate

        ax.plot(time_frames, self.joint_angles['knee'], 'b-', label='膝关节', linewidth=2)
        ax.axvline(x=self.takeoff_frame / self.frame_rate, color='green', linestyle='--', alpha=0.7)
        ax.axvline(x=self.landing_frame / self.frame_rate, color='red', linestyle='--', alpha=0.7)

        ax.set_xlabel('时间 (秒)')
        ax.set_ylabel('角度 (度)')
        ax.set_title('膝关节角度变化')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 3. 躯干倾斜角
        ax = axes[0, 2]
        ax.plot(time_frames, self.joint_angles['trunk'], 'purple', linewidth=2)
        ax.axhline(y=90, color='gray', linestyle=':', alpha=0.5, label='垂直')
        ax.axvline(x=self.takeoff_frame / self.frame_rate, color='green', linestyle='--', alpha=0.7)
        ax.axvline(x=self.landing_frame / self.frame_rate, color='red', linestyle='--', alpha=0.7)

        ax.set_xlabel('时间 (秒)')
        ax.set_ylabel('角度 (度)')
        ax.set_title('躯干倾斜角变化')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 4. 水平速度
        ax = axes[1, 0]
        ax.plot(time_frames, self.vx, 'navy', linewidth=2)
        ax.axvline(x=self.takeoff_frame / self.frame_rate, color='green', linestyle='--', alpha=0.7, label='起跳')
        ax.axvline(x=self.landing_frame / self.frame_rate, color='red', linestyle='--', alpha=0.7, label='落地')
        ax.axhline(y=0, color='gray', linestyle=':', alpha=0.5)

        ax.fill_between(time_frames[self.takeoff_frame:self.landing_frame + 1],
                        self.vx[self.takeoff_frame:self.landing_frame + 1],
                        alpha=0.3, color='cyan')

        ax.set_xlabel('时间 (秒)')
        ax.set_ylabel('速度 (像素/秒)')
        ax.set_title('水平速度变化')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 5. 垂直速度
        ax = axes[1, 1]
        ax.plot(time_frames, -self.vy, 'darkred', linewidth=2)
        ax.axvline(x=self.takeoff_frame / self.frame_rate, color='green', linestyle='--', alpha=0.7, label='起跳')
        ax.axvline(x=self.landing_frame / self.frame_rate, color='red', linestyle='--', alpha=0.7, label='落地')
        ax.axhline(y=0, color='gray', linestyle=':', alpha=0.5)

        ax.fill_between(time_frames[self.takeoff_frame:self.landing_frame + 1],
                        -self.vy[self.takeoff_frame:self.landing_frame + 1],
                        alpha=0.3, color='pink')

        ax.set_xlabel('时间 (秒)')
        ax.set_ylabel('速度 (向上为正，像素/秒)')
        ax.set_title('垂直速度变化')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 6. 滞空阶段分析
        ax = axes[1, 2]

        # 绘制滞空三阶段
        phase_names = list(self.flight_phases.keys())
        phase_heights = [self.flight_phases[p]['重心高度变化'] for p in phase_names]

        bars = ax.bar(phase_names, phase_heights, color=['green', 'blue', 'red'], alpha=0.7)
        ax.set_ylabel('重心高度变化 (像素)')
        ax.set_title('滞空三阶段重心变化')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        # 添加数值标签
        for bar, height in zip(bars, phase_heights):
            ax.text(bar.get_x() + bar.get_width() / 2, height,
                    f'{height:.1f}', ha='center', va='bottom' if height > 0 else 'top')

        plt.tight_layout()
        return fig

    def generate_report(self):
        """生成分析报告（不包含建议）"""

        report = f"""
{self.athlete_name} 立定跳远动作分析报告
{'=' * 60}

一、基本信息
- 实际成绩: {self.actual_score}米
- 起跳时刻: 第{self.takeoff_frame}帧 ({self.takeoff_frame / self.frame_rate:.3f}秒)
- 落地时刻: 第{self.landing_frame}帧 ({self.landing_frame / self.frame_rate:.3f}秒)
- 滞空时间: {self.flight_time:.3f}秒

二、滞空阶段分析

1. 上升期 (前30%滞空时间)
   - 重心高度变化: {self.flight_phases['上升期']['重心高度变化']:.1f}像素
   - 平均膝关节角度: {self.flight_phases['上升期']['平均膝角']:.1f}°
   - 平均躯干倾斜: {self.flight_phases['上升期']['平均躯干角']:.1f}°

2. 最高点期 (中间40%滞空时间)
   - 重心高度变化: {self.flight_phases['最高点期']['重心高度变化']:.1f}像素
   - 平均膝关节角度: {self.flight_phases['最高点期']['平均膝角']:.1f}°
   - 平均躯干倾斜: {self.flight_phases['最高点期']['平均躯干角']:.1f}°

3. 下降期 (后30%滞空时间)
   - 重心高度变化: {self.flight_phases['下降期']['重心高度变化']:.1f}像素
   - 平均膝关节角度: {self.flight_phases['下降期']['平均膝角']:.1f}°
   - 平均躯干倾斜: {self.flight_phases['下降期']['平均躯干角']:.1f}°

三、关键技术参数
- 起跳瞬间水平速度: {self.vx[self.takeoff_frame]:.1f}像素/秒
- 起跳瞬间垂直速度: {-self.vy[self.takeoff_frame]:.1f}像素/秒
- 起跳角度: {np.arctan2(-self.vy[self.takeoff_frame], self.vx[self.takeoff_frame]) * 180 / np.pi:.1f}°
- 最大飞行高度: {self.com_y[self.takeoff_frame] - np.min(self.com_y[self.takeoff_frame:self.landing_frame + 1]):.1f}像素
- 水平飞行距离: {abs(self.com_x[self.landing_frame] - self.com_x[self.takeoff_frame]):.1f}像素
"""
        return report

    def comprehensive_analysis(self):
        """执行完整分析"""
        print(f"\n开始分析{self.athlete_name} (实际成绩: {self.actual_score}m)")
        print("=" * 50)

        # 预处理
        self.preprocess_data()

        # 检测关键时刻
        self.detect_takeoff_and_landing()

        # 分析身体运动
        self.analyze_body_parts_movement()

        # 生成可视化
        fig = self.create_visualization()
        fig.savefig(f'{self.athlete_name}_分析结果.png', dpi=150, bbox_inches='tight')
        plt.close(fig)
        print(f"分析图已保存: {self.athlete_name}_分析结果.png")

        # 生成报告
        report = self.generate_report()
        print(report)

        # 保存报告
        with open(f'{self.athlete_name}_分析报告.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存: {self.athlete_name}_分析报告.txt")

        return self.results


# 主程序
if __name__ == "__main__":
    print("立定跳远AI姿态分析系统")
    print("=" * 60)

    # 运动者数据
    athletes_data = {
        '运动者1': {
            'path': r'C:\Users\<USER>\Desktop\E题\附件\附件1\运动者1的跳远位置信息.xlsx',
            'score': 1.58
        },
        '运动者2': {
            'path': r'C:\Users\<USER>\Desktop\E题\附件\附件1\运动者2的跳远位置信息.xlsx',
            'score': 1.15
        }
    }

    analyzers = {}

    # 分析每位运动者
    for name, info in athletes_data.items():
        print(f"\n{'=' * 30}")
        print(f"分析{name}")
        print('=' * 30)

        try:
            analyzer = CompleteJumpAnalyzer(info['path'], name, info['score'])
            results = analyzer.comprehensive_analysis()
            analyzers[name] = analyzer

        except Exception as e:
            print(f"分析{name}失败: {e}")
            import traceback

            traceback.print_exc()

    # 对比分析
    if len(analyzers) == 2:
        print("\n" + "=" * 60)
        print("对比分析")
        print("=" * 60)

        a1 = analyzers['运动者1']
        a2 = analyzers['运动者2']

        print(f"\n成绩对比:")
        print(f"  运动者1: {a1.actual_score}m")
        print(f"  运动者2: {a2.actual_score}m")
        print(f"  差异: {a1.actual_score - a2.actual_score:.2f}m")

        print(f"\n滞空时间对比:")
        print(f"  运动者1: {a1.flight_time:.3f}秒")
        print(f"  运动者2: {a2.flight_time:.3f}秒")
        print(f"  差异: {a1.flight_time - a2.flight_time:.3f}秒")

        print(f"\n起跳角度对比:")
        angle1 = np.arctan2(-a1.vy[a1.takeoff_frame], a1.vx[a1.takeoff_frame]) * 180 / np.pi
        angle2 = np.arctan2(-a2.vy[a2.takeoff_frame], a2.vx[a2.takeoff_frame]) * 180 / np.pi
        print(f"  运动者1: {angle1:.1f}°")
        print(f"  运动者2: {angle2:.1f}°")